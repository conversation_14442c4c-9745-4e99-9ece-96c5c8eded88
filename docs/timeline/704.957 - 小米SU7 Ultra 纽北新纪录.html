<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7:04.957 - 小米SU7 Ultra 纽北新纪录</title>
    
    <!-- TailwindCSS 3.0+ via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for Icons via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@700;900&family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Thematic adaptation of Style 5 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@700;900&display=swap');
      
        body {
            font-family: 'Noto Sans SC', 'Roboto', sans-serif;
            background-color: #050810;
            color: #E0E0E0;
            overflow-x: hidden;
        }
  
        .glass-card {
            background: rgba(25, 30, 50, 0.4);
            backdrop-filter: blur(50px);
            -webkit-backdrop-filter: blur(50px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 2rem;
            transition: transform 0.3s ease-out, box-shadow 0.3s ease-out;
            will-change: transform;
            z-index: 10;
            position: relative;
            overflow: hidden;
            padding: 2rem;
        }

        .glass-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
  
        .glass-card > * { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6); }
  
        .blob-container {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            overflow: hidden; z-index: -1; filter: blur(120px);   
        }
  
        .blob {
            position: absolute; border-radius: 50%; opacity: 0.6; will-change: transform;
        }
        /* New color palette for speed and passion */
        .blob-1 { background: #00A3FF; width: 30vw; height: 30vw; top: 5vh; left: 65vw; animation: move-blob-1 25s ease-in-out infinite alternate; }
        .blob-2 { background: #D946EF; width: 25vw; height: 25vw; top: 50vh; left: 5vw; animation: move-blob-2 30s ease-in-out infinite alternate; }
        .blob-3 { background: #FF6B00; /* Fiery Orange */ width: 35vw; height: 35vw; top: 20vh; left: 20vw; animation: move-blob-3 28s ease-in-out infinite alternate; }
  
        @keyframes move-blob-1 { from { transform: translate(0, 0) scale(1); } to { transform: translate(-15vw, 10vh) scale(1.2); } }
        @keyframes move-blob-2 { from { transform: translate(0, 0) scale(1); } to { transform: translate(20vw, -15vh) scale(0.9); } }
        @keyframes move-blob-3 { from { transform: translate(0, 0) scale(1) rotate(0deg); } to { transform: translate(-10vw, 15vh) scale(1.1) rotate(90deg); } }
      
        .huge-text {
            font-size: clamp(10rem, 28vw, 24rem);
            line-height: 1;
            font-family: 'Roboto', sans-serif;
            font-weight: 700;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255, 255, 255, 0.05);
            z-index: 1;
            pointer-events: none;
        }
  
        /* Nürburgring Track Animation */
        .track-path {
            stroke: rgba(0, 255, 255, 0.7);
            stroke-width: 3;
            stroke-dasharray: 4000;
            stroke-dashoffset: 4000;
            animation: draw-track 8s ease-out forwards;
            animation-delay: 1s;
        }
        @keyframes draw-track { to { stroke-dashoffset: 0; } }
        
        /* Entrance animation for cards */
        .fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out forwards;
        }
        @keyframes fadeInUp { to { opacity: 1; transform: translateY(0); } }
        
        .delay-1 { animation-delay: 0.2s; }
        .delay-2 { animation-delay: 0.4s; }
        .delay-3 { animation-delay: 0.6s; }
        .delay-4 { animation-delay: 0.8s; }

        .highlight-orange { color: #FF8C00; }
    </style>
</head>
<body class="min-h-screen w-full p-4 sm:p-8 lg:p-12 flex items-center justify-center">
    
    <div class="blob-container">
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
        <div class="blob blob-3"></div>
    </div>

    <div class="huge-text">RECORD</div>

    <main class="w-full max-w-7xl mx-auto space-y-8 lg:space-y-10 z-10 text-center">

        <!-- Lap Time Hero Section -->
        <section class="fade-in-up">
            <div class="flex justify-center items-end gap-x-2 md:gap-x-4 font-black leading-none">
                <span class="text-[12vw] md:text-9xl">7</span>
                <span class="text-[8vw] md:text-7xl text-white/80 pb-1">:04</span>
                <span class="text-[6vw] md:text-5xl highlight-orange pb-1">.957</span>
            </div>
            <h1 class="text-4xl md:text-5xl font-black mt-4">#小米SU7 Ultra#</h1>
            <h2 class="text-2xl md:text-3xl font-bold text-cyan-300 tracking-wider mt-2">纽北最快量产电动车</h2>
            <p class="text-sm text-white/50 tracking-widest mt-1">NÜRBURGRING FASTEST PRODUCTION EV</p>
        </section>

        <!-- Lei Jun's Quote -->
        <section class="fade-in-up delay-1 max-w-3xl mx-auto">
            <div class="glass-card">
                <p class="text-2xl md:text-3xl font-bold">“我为我们的团队，感到无比骄傲！”</p>
                <p class="mt-3 text-lg text-white/70">- 雷军</p>
            </div>
        </section>
        
        <!-- Nürburgring Track SVG -->
        <section class="fade-in-up delay-2 max-w-4xl mx-auto">
            <div class="glass-card p-4 md:p-6">
                 <svg viewBox="0 0 500 350" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <title>Nürburgring Nordschleife Outline</title>
                    <path class="track-path" d="M250.2,27.3c-28.7,0.3-54.3,12.5-72.1,33.1c-15.6,18.1-23.7,40-23.9,62.8c-0.3,27.5,9.4,54.8,27.5,75.1c11.9,13.3,27,23.8,43.9,30.3c21.5,8.3,44.7,10.1,66.9,4.9c14.2-3.3,27.4-9.3,39.2-17.6c16.5-11.6,29.8-27.8,38.2-46.1c5.2-11.4,8.2-23.6,8.8-36.1c0.8-15.9-2.3-31.9-9.1-46.3c-10.3-22-27.9-39.7-49.8-49.8C302,26.1,276.1,26.9,250.2,27.3z M145.7,117.9c-2.3-15.3,1.4-30.8,10.2-43.5c12.2-17.7,31.7-28.8,53.2-29.3c16.1-0.4,31.7,4.3,44.5,13.2c16.2,11.5,27.5,28.8,31.9,48.5c2.9,13,2.4,26.4-1.3,39.1c-6,20-19.1,37.3-36.8,48.8c-14.4,9.4-31,14.6-48.2,14.6c-20.3,0-39.9-7.1-55.2-19.7c-17.7-14.6-29.4-34.9-32.5-57.1C145.9,122.9,145.7,120.4,145.7,117.9z M487.6,175.2c-2.3-10.9-10.3-19.4-21.2-21.6c-13.8-2.7-27.4-0.1-39.3,6.3c-12.8,6.8-23.1,16.8-30.1,28.9c-10.3,17.8-14.8,38.3-12.8,58.6c1.2,12.3,5.6,24.1,12.7,34.4c10.4,15,25.2,26.6,42.4,32.7c18.1,6.4,37.6,6.7,55.8,1c14-4.4,26.8-11.9,37.3-21.9c15.2-14.5,25.7-33.3,29.9-53.9c3.1-15.2,1.8-30.9-3.7-45.5C497.1,192.3,493.1,183.1,487.6,175.2z M45.8,222.1c11.9-9.5,27.1-15.2,43-15.8c20-0.7,39.4,6.7,53.2,19.9c16.3,15.5,26.7,36.5,29,59.2c1.4,14-1.8,28.1-9,40.4c-9.1,15.5-23.5,27.5-40.6,33.7c-18.1,6.5-37.7,6.8-56,0.9c-16.7-5.4-31.5-15.5-42.3-29.1c-13.8-17.3-21.2-38.6-20.6-60.2C1.7,250.7,18.4,231.7,45.8,222.1z"/>
                 </svg>
            </div>
        </section>
        
        <!-- Supporting Facts Grid -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            <div class="glass-card fade-in-up delay-2">
                <i class="fas fa-car-side text-5xl highlight-orange"></i>
                <h3 class="text-2xl font-bold mt-4">量产版</h3>
                <p class="text-white/70 mt-1">PRODUCTION MODEL</p>
                <p class="text-sm mt-2 text-white/60">非改装，即你所见的性能</p>
            </div>
            <div class="glass-card fade-in-up delay-3">
                <i class="fas fa-check-circle text-5xl text-cyan-300"></i>
                <h3 class="text-2xl font-bold mt-4">官方认证</h3>
                <p class="text-white/70 mt-1">OFFICIAL CERTIFICATION</p>
                <p class="text-sm mt-2 text-white/60">纽北官方赛道视频为证</p>
            </div>
            <div class="glass-card fade-in-up delay-4">
                <i class="fas fa-film text-5xl highlight-orange"></i>
                <h3 class="text-2xl font-bold mt-4">一刀未剪</h3>
                <p class="text-white/70 mt-1">UNCUT FOOTAGE</p>
                <p class="text-sm mt-2 text-white/60">全程记录，真实可信</p>
            </div>
        </section>

        <!-- Final Emotion -->
        <section class="fade-in-up delay-4 max-w-3xl mx-auto">
             <div class="glass-card flex items-center justify-center gap-4">
                 <i class="fas fa-heart-pulse text-4xl text-red-500 animate-pulse"></i>
                 <p class="text-xl md:text-2xl font-bold">“再看依然心跳加速，热血沸腾。”</p>
             </div>
        </section>

    </main>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>