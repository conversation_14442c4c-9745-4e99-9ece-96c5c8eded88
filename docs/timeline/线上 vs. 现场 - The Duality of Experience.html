<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线上 vs. 现场 - The Duality of Experience</title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@700;900&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #050810;
            color: #E0E0E0;
        }

        .glass-card {
            background: rgba(25, 30, 50, 0.4);
            backdrop-filter: blur(50px);
            -webkit-backdrop-filter: blur(50px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 2rem;
            transition: transform 0.3s ease-out;
            will-change: transform;
            z-index: 10;
            position: relative;
            overflow: hidden;
            padding: 2rem;
        }

        .glass-card > * { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6); }

        .blob-container {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            overflow: hidden; z-index: -1; filter: blur(100px); 
        }

        .blob {
            position: absolute; border-radius: 50%; opacity: 0.7; will-change: transform;
        }
        .blob-1 { background: #00A3FF; width: 30vw; height: 30vw; top: 10vh; left: 10vw; animation: move-blob-1 30s ease-in-out infinite; }
        .blob-2 { background: #D946EF; width: 35vw; height: 35vw; top: 40vh; left: 60vw; animation: move-blob-2 35s ease-in-out infinite; }

        @keyframes move-blob-1 { 0%{transform:translate(0,0) scale(1)} 50%{transform:translate(15vw,20vh) scale(1.2)} 100%{transform:translate(0,0) scale(1)} }
        @keyframes move-blob-2 { 0%{transform:translate(0,0) scale(1)} 50%{transform:translate(-20vw,-15vh) scale(.8)} 100%{transform:translate(0,0) scale(1)} }
        
        .huge-text {
            font-size: clamp(8rem, 20vw, 18rem);
            line-height: 1;
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0.1;
            z-index: 1;
            pointer-events: none;
        }

        .network-line {
            stroke-dasharray: 500;
            stroke-dashoffset: 500;
            animation: draw-line 4s ease-out forwards;
        }
        .network-node {
            opacity: 0;
            animation: pulse-node 2s ease-in-out infinite;
        }
        @keyframes draw-line { to { stroke-dashoffset: 0; } }
        @keyframes pulse-node { 0%, 100% { opacity: 0.8; r: 4; } 50% { opacity: 1; r: 6; } }

        .binary-stream {
            position: absolute;
            top: -20px;
            font-family: monospace;
            font-size: 1rem;
            color: rgba(0, 255, 255, 0.5);
            writing-mode: vertical-rl;
            text-orientation: upright;
            user-select: none;
            z-index: 1;
            animation: fall 10s linear infinite;
        }
        @keyframes fall {
            to { transform: translateY(120vh); }
        }
    </style>
</head>
<body class="w-full min-h-screen flex justify-center p-4 sm:p-6 lg:p-8">

    <div class="blob-container">
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
    </div>

    <main id="content-grid" class="w-full max-w-screen-2xl grid grid-cols-12 gap-6">

        <!-- Card 1: 开篇 -->
        <div class="glass-card col-span-12 flex flex-col md:flex-row justify-between items-center text-center md:text-left">
            <h2 class="text-3xl md:text-4xl font-bold">线上看直播，是<span class="text-cyan-300">二维的</span></h2>
            <p class="text-white/50 text-xl mx-8 my-4 md:my-0">VS</p>
            <h2 class="text-3xl md:text-4xl font-bold">现场看直播，是<span class="text-fuchsia-400">三维的</span></h2>
        </div>

        <!-- 左侧: 线上 -->
        <div class="glass-card col-span-12 md:col-span-6 row-span-3 flex flex-col justify-between">
            <div id="binary-container" class="absolute inset-0 opacity-20 overflow-hidden"></div>
            <p class="huge-text text-cyan-300">2D</p>
            <div class="relative z-10">
                <p class="text-3xl lg:text-4xl font-bold">你在<span class="text-cyan-300">下载数据</span></p>
                <p class="text-white/60 text-base uppercase mt-1">You are downloading data</p>
            </div>
            <ul class="relative z-10 space-y-6 text-xl lg:text-2xl font-bold text-right">
                <li class="flex items-center justify-end gap-4"><p>一个连接大脑</p><i class="fas fa-brain w-8 text-cyan-300"></i></li>
                <li class="flex items-center justify-end gap-4"><p>一个寻找答案</p><i class="fas fa-magnifying-glass w-8 text-cyan-300"></i></li>
                <li class="flex items-center justify-end gap-4"><p>一个追求效率</p><i class="fas fa-bolt w-8 text-cyan-300"></i></li>
            </ul>
        </div>

        <!-- 右侧: 现场 -->
        <div class="glass-card col-span-12 md:col-span-6 row-span-3 flex flex-col justify-between">
            <svg class="absolute inset-0 w-full h-full z-1" width="100%" height="100%" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line class="network-line" style="animation-delay: 0.2s" x1="50" y1="50" x2="200" y2="150" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <line class="network-line" style="animation-delay: 0.5s" x1="200" y1="150" x2="100" y2="350" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <line class="network-line" style="animation-delay: 0.8s" x1="100" y1="350" x2="350" y2="300" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <line class="network-line" style="animation-delay: 1.1s" x1="350" y1="300" x2="250" y2="80" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <line class="network-line" style="animation-delay: 1.4s" x1="250" y1="80" x2="50" y2="50" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <line class="network-line" style="animation-delay: 1.7s" x1="200" y1="150" x2="350" y2="300" stroke="#D946EF" stroke-width="2" stroke-opacity="0.8"/>
                <circle class="network-node" style="animation-delay: 0.2s" cx="50" cy="50" r="4" fill="#F472B6"/>
                <circle class="network-node" style="animation-delay: 0.5s" cx="200" cy="150" r="4" fill="#F472B6"/>
                <circle class="network-node" style="animation-delay: 0.8s" cx="100" cy="350" r="4" fill="#F472B6"/>
                <circle class="network-node" style="animation-delay: 1.1s" cx="350" cy="300" r="4" fill="#F472B6"/>
                <circle class="network-node" style="animation-delay: 1.4s" cx="250" cy="80" r="4" fill="#F472B6"/>
            </svg>
            <p class="huge-text text-fuchsia-400">3D</p>
            <div class="relative z-10">
                <p class="text-3xl lg:text-4xl font-bold">你在<span class="text-fuchsia-400">链接生命</span></p>
                <p class="text-white/60 text-base uppercase mt-1">You are linking life</p>
            </div>
            <ul class="relative z-10 space-y-6 text-xl lg:text-2xl font-bold text-left">
                <li class="flex items-center gap-4"><i class="fas fa-hand-sparkles w-8 text-fuchsia-400"></i><p>一个触及灵魂</p></li>
                <li class="flex items-center gap-4"><i class="fas fa-lightbulb w-8 text-fuchsia-400"></i><p>一个发现问题</p></li>
                <li class="flex items-center gap-4"><i class="fas fa-dice-d6 w-8 text-fuchsia-400"></i><p>一个拥抱偶然</p></li>
            </ul>
        </div>

        <!-- Card 3: 总结 -->
        <div class="glass-card col-span-12 flex flex-col justify-center items-center text-center space-y-2">
            <p class="text-2xl lg:text-3xl text-white/80">说到底，线上你看的是“直播”。</p>
            <p class="text-4xl lg:text-6xl font-black text-white">现场，你 <span class="text-fuchsia-400">活在“直播”里</span>。</p>
            <p class="text-white/60 text-base uppercase tracking-widest pt-2">Ultimately, you watch the 'live' online. In person, you live in the 'live'.</p>
        </div>

    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Parallax effect for cards
            const cards = document.querySelectorAll('.glass-card');
            const parallaxIntensity = 10;
            document.body.addEventListener('mousemove', (e) => {
                const x = (e.clientX / window.innerWidth) - 0.5;
                const y = (e.clientY / window.innerHeight) - 0.5;
                cards.forEach(card => {
                    const transformX = x * parallaxIntensity;
                    const transformY = y * parallaxIntensity;
                    card.style.transform = `translate(${transformX}px, ${transformY}px) scale(1)`;
                });
            });

            // Binary stream generator
            const binaryContainer = document.getElementById('binary-container');
            if (binaryContainer) {
                const createStream = () => {
                    const stream = document.createElement('div');
                    stream.className = 'binary-stream';
                    stream.style.left = `${Math.random() * 100}%`;
                    stream.style.animationDuration = `${Math.random() * 5 + 5}s`; // 5 to 10 seconds
                    stream.style.animationDelay = `${Math.random() * 5}s`;
                    stream.innerHTML = Array(Math.floor(Math.random() * 20 + 10)).fill(0).map(() => Math.round(Math.random())).join('<br>');
                    binaryContainer.appendChild(stream);
                    
                    setTimeout(() => {
                        stream.remove();
                    }, 10000); // Remove after animation ends
                };
                setInterval(createStream, 200);
            }
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>