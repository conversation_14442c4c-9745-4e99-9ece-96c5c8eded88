<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨中行 | Walking in the Rain</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    
    <!-- Font Awesome for Icons via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Fonts: Noto Sans SC & Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Inter:wght@400;700;900&display=swap" rel="stylesheet">

    <style>
        /* --- 自定义全局样式 --- */
        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: #f9fafb; /* 极浅灰色背景 */
            color: #1f2937; /* 深灰色文字 */
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* --- 渐变高亮色定义 --- */
        .gradient-text-vibrant {
            background-image: linear-gradient(120deg, #3b82f6 0%, #a855f7 50%, #ec4899 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg-accent {
             background-image: linear-gradient(120deg, #3b82f6 0%, #a855f7 100%);
        }

        /* --- 滚动入场动画 --- */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1.2s cubic-bezier(0.16, 1, 0.3, 1), transform 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            transition-delay: 0.2s;
        }

        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* --- 装饰性辉光元素 --- */
        .glow-element {
            position: absolute;
            border-radius: 9999px;
            filter: blur(160px);
            z-index: -10;
            pointer-events: none;
        }

        /* --- 简洁勾线SVG样式 --- */
        .outline-graphic {
            stroke: #9ca3af; /* 中灰色描边 */
            stroke-width: 1.5;
            transition: all 0.5s ease-in-out;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

    </style>
</head>
<body class="antialiased">

    <!-- 主容器 -->
    <main class="relative z-10">

        <!-- Section 1: 问题 - 出离的本质 -->
        <section class="min-h-screen flex flex-col items-center justify-center p-8 overflow-hidden">
            <!-- 背景辉光装饰 -->
            <div class="glow-element w-[500px] h-[500px] bg-blue-400/20 top-1/4 left-1/4"></div>
            
            <div class="text-center scroll-reveal">
                <!-- 抽象的“门”或“通道”图形，代表探索本质 -->
                <svg width="100" height="100" viewBox="0 0 100 100" class="mx-auto mb-8 opacity-70">
                    <path d="M30 90 V 10 H 70 V 90" class="outline-graphic" />
                    <path d="M30 10 C 30 30, 70 30, 70 10" class="outline-graphic" />
                </svg>
                <h1 class="text-6xl md:text-8xl font-black tracking-tight">出离的本质<br>是什么？</h1>
                <p class="text-xl md:text-2xl font-bold text-gray-400 tracking-widest mt-6">THE ESSENCE OF LETTING GO</p>
            </div>
        </section>

        <!-- Section 2: 答案 - 放下评判的你 -->
        <section class="py-24 md:py-40">
            <div class="max-w-7xl mx-auto px-8">
                <div class="grid md:grid-cols-2 gap-16 md:gap-24 items-center">
                    
                    <!-- 左侧：不是放下世界 -->
                    <div class="text-center md:text-left scroll-reveal">
                         <i class="fa-solid fa-globe text-8xl text-gray-300 mb-6"></i>
                        <h2 class="text-5xl md:text-6xl font-black tracking-tight">不是放下世界</h2>
                        <p class="text-2xl md:text-3xl font-bold text-gray-400 mt-2">NOT TO ABANDON THE WORLD</p>
                    </div>

                    <!-- 右侧：是放下那个评判的你 -->
                    <div class="text-center md:text-left bg-white p-12 rounded-3xl shadow-2xl shadow-gray-200 scroll-reveal" style="transition-delay: 0.3s;">
                        <i class="fa-solid fa-scale-unbalanced text-8xl gradient-text-vibrant mb-6"></i>
                        <h2 class="text-5xl md:text-6xl font-black tracking-tight">是放下那个<br>“<span class="gradient-text-vibrant">评判</span>”的你</h2>
                         <p class="text-2xl md:text-3xl font-bold text-gray-400 mt-2">BUT THE 'YOU' THAT JUDGES IT</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: 方法 - 不打伞，不跑 -->
        <section class="bg-gray-900 text-gray-200 py-24 md:py-32 my-20 md:my-32 rounded-[48px] mx-4 md:mx-auto md:max-w-7xl overflow-hidden relative">
            <!-- 背景辉光装饰 -->
            <div class="glow-element w-[700px] h-[700px] bg-purple-600/25 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            
            <div class="relative z-10 max-w-5xl mx-auto px-8 text-center scroll-reveal">
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mb-4">HOW TO DO IT?</p>
                <h2 class="text-6xl md:text-9xl font-black text-white leading-tight">雨来了</h2>
                
                <div class="mt-16 grid grid-cols-1 md:grid-cols-2 gap-12 text-center">
                    <div class="scroll-reveal">
                        <i class="fa-solid fa-umbrella text-8xl text-gray-600 mb-4"></i>
                        <p class="text-4xl font-bold text-gray-400">不打伞</p>
                        <p class="text-lg font-bold tracking-widest text-gray-500">NO UMBRELLA</p>
                    </div>
                    <div class="scroll-reveal" style="transition-delay: 0.3s;">
                        <i class="fa-solid fa-person-running text-8xl text-gray-600 mb-4"></i>
                        <p class="text-4xl font-bold text-gray-400">也不跑</p>
                        <p class="text-lg font-bold tracking-widest text-gray-500">NO RUNNING</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: 行动 - 在雨中行走 -->
        <section class="min-h-screen flex flex-col items-center justify-center p-8 overflow-hidden">
             <!-- 背景辉光装饰 -->
            <div class="glow-element w-[600px] h-[600px] bg-pink-400/25 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            
            <div class="text-center scroll-reveal">
                <p class="text-5xl md:text-7xl font-bold text-gray-400 mb-6">只是</p>
                <p class="text-xl md:text-2xl font-bold text-gray-400 tracking-widest mb-10">JUST</p>

                <!-- 行走的SVG -->
                 <svg width="80" height="80" viewBox="0 0 24 24" class="mx-auto mb-8">
                    <defs>
                        <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#3b82f6;" />
                            <stop offset="100%" style="stop-color:#a855f7;" />
                        </linearGradient>
                    </defs>
                    <path d="M14.5 5.5C14.5 6.32843 13.8284 7 13 7C12.1716 7 11.5 6.32843 11.5 5.5C11.5 4.67157 12.1716 4 13 4C13.8284 4 14.5 4.67157 14.5 5.5Z" stroke="url(#iconGradient)" stroke-width="1.5"/>
                    <path d="M13 7V13.5L16 20" class="outline-graphic" stroke="url(#iconGradient)"/>
                    <path d="M13 13.5L8.5 17.5" class="outline-graphic" stroke="url(#iconGradient)"/>
                </svg>
                
                <h2 class="text-6xl md:text-9xl font-black tracking-tight">
                    在雨中<br><span class="gradient-text-vibrant">行走</span>
                </h2>

                <div class="mt-16">
                    <a href="#" class="inline-block px-10 py-5 text-xl font-bold text-white rounded-2xl shadow-2xl shadow-blue-500/20 transition-transform duration-300 hover:scale-105 gradient-bg-accent">
                        Embrace the Rain <i class="fa-solid fa-droplet ml-2"></i>
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="text-center py-12">
            <p class="text-gray-400">It's not about the storm, but how you walk through it.</p>
        </footer>

    </main>

    <script>
        // --- 简单的滚动入场动画脚本 ---
        document.addEventListener("DOMContentLoaded", function() {
            const reveals = document.querySelectorAll('.scroll-reveal');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15
            });

            reveals.forEach(reveal => {
                observer.observe(reveal);
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>