<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>你的大脑不是硬盘，而是一座花园</title>
	<script src="https://cdn.tailwindcss.com/3.4.1"></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
	<style>
		@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap');
		body {
			font-family: 'Noto Sans SC', sans-serif;
			background-color: #ffffff;
			color: #1a1a1a;
		}
		.gradient-text {
			background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
		}
		.gradient-bg {
			background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
		}
		.fade-in {
			opacity: 0;
			transform: translateY(30px);
			transition: opacity 0.6s ease-out, transform 0.6s ease-out;
		}
		.fade-in.visible {
			opacity: 1;
			transform: translateY(0);
		}
		.highlight-gradient {
			background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2), rgba(236, 72, 153, 0.2));
		}
	</style>
</head>
<body class="bg-white">

<div class="container mx-auto px-6 py-12 md:py-20 max-w-7xl">

	<!-- Hero Section -->
	<header class="text-center mb-20 md:mb-32 fade-in">
		<h1 class="text-5xl md:text-7xl lg:text-8xl font-black tracking-tight leading-tight">
			你的大脑不是硬盘<br>而是一座<span class="gradient-text">花园</span>
		</h1>
		<p class="mt-6 text-xl md:text-2xl font-bold text-gray-800">
			重塑记忆的 <span class="english-font text-3xl mx-1">10</span> 个黄金习惯
		</p>
		<p class="mt-2 text-sm text-gray-500 uppercase tracking-widest">Reshape Your Memory with 10 Golden Habits</p>
	</header>

	<!-- Intro Section -->
	<section class="mb-20 md:mb-32 space-y-12">
		<div class="text-center fade-in">
			<h2 class="text-2xl md:text-3xl font-bold mb-8">你是否经历过这样的瞬间？</h2>
			<div class="grid md:grid-cols-3 gap-8 text-lg text-gray-700">
				<div class="bg-gray-50 p-6 rounded-xl">话在嘴边，却怎么也想不起那个熟悉的名字。</div>
				<div class="bg-gray-50 p-6 rounded-xl">刚放下钥匙，回头就忘了放在哪里，仿佛被小精灵藏了起来。</div>
				<div class="bg-gray-50 p-6 rounded-xl">读完一本书，合上时脑中一片空白，只剩下“读过”这个事实。</div>
			</div>
		</div>
		<div class="text-center max-w-4xl mx-auto fade-in">
			<p class="text-2xl md:text-4xl font-bold my-8 leading-relaxed">
				世界上没有“坏的记忆力”<br>只有“<span class="gradient-text">未经训练的记忆力</span>”
			</p>
			<p class="text-lg text-gray-600">
				我们总把大脑想象成一块硬盘，信息存进去，用的时候再提取。但这个比喻从根本上就是错的。你的大脑更像一座花园，需要你像园丁一样去播种、浇灌、修剪和培育。记忆力，是你精心园艺的成果。
			</p>
		</div>
	</section>

	<!-- Habits Sections -->
	<div class="space-y-24">
		<!-- Part 1 -->
		<section class="fade-in">
			<div class="text-center mb-12">
				<h3 class="text-3xl md:text-4xl font-bold">第一部分：播种与耕耘</h3>
				<p class="text-gray-500 mt-2 text-lg">INPUT PHASE: SOWING & CULTIVATION</p>
			</div>
			<div class="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
				<!-- Habit 1-3 -->
				<div class="bg-gray-50 p-8 rounded-2xl border border-gray-100 flex flex-col">
					<div class="flex items-center mb-4">
						<i class="fa-solid fa-lightbulb text-3xl gradient-text mr-4"></i>
						<h4 class="text-2xl font-bold">01 聚光灯法则</h4>
					</div>
					<h5 class="font-bold mb-2">它是什么？</h5>
					<p class="text-gray-600 mb-4 flex-grow">与其说是记忆习惯，不如说是专注力习惯。记忆的起点不是“记”，而是“看清”。聚光灯法则，就是把你的全部注意力，像一束舞台聚光灯一样，只打在你需要记忆的对象上。</p>
					<h5 class="font-bold mb-2">如何实践？</h5>
					<ul class="list-disc list-inside text-gray-800 space-y-1">
						<li><strong>单任务处理：</strong>阅读时关掉手机通知，与人交谈时直视对方。</li>
						<li><strong>番茄工作法：</strong>设定25分钟专注时间，创造高质量输入环境。</li>
					</ul>
				</div>
				<div class="bg-gray-50 p-8 rounded-2xl border border-gray-100 flex flex-col">
					<div class="flex items-center mb-4">
						<i class="fa-solid fa-link text-3xl gradient-text mr-4"></i>
						<h4 class="text-2xl font-bold">02 知识炼金术</h4>
					</div>
					<h5 class="font-bold mb-2">它是什么？</h5>
					<p class="text-gray-600 mb-4 flex-grow">孤立的信息是沙子，风一吹就散。有关联的信息是混凝土。知识炼金术，就是把新知识与你已知的旧知识“焊接”在一起，赋予它意义和位置。</p>
					<h5 class="font-bold mb-2">如何实践？</h5>
					<ul class="list-disc list-inside text-gray-800 space-y-1">
						<li><strong>提问与关联：</strong>问自己“这像什么？”“有什么区别？”“能用在哪？”</li>
						<li><strong>类比与比喻：</strong>创造自己的比喻，这个过程就是最深刻的记忆。</li>
					</ul>
				</div>
				<div class="bg-gray-50 p-8 rounded-2xl border border-gray-100 flex flex-col">
					<div class="flex items-center mb-4">
						<i class="fa-solid fa-map-location-dot text-3xl gradient-text mr-4"></i>
						<h4 class="text-2xl font-bold">03 记忆宫殿</h4>
					</div>
					<h5 class="font-bold mb-2">它是什么？</h5>
					<p class="text-gray-600 mb-4 flex-grow">将信息转化为生动、夸张的图像，然后“摆放”在一个你极为熟悉的虚拟空间里（比如你的家）。</p>
					<h5 class="font-bold mb-2">如何实践？</h5>
					<ul class="list-disc list-inside text-gray-800 space-y-1">
						<li><strong>规划路线：</strong>想象你从家门口走进卧室的路线。</li>
						<li><strong>放置信息：</strong>想象门口泼满牛奶，沙发长出面包树。画面越离奇，记忆越深刻。</li>
					</ul>
				</div>
			</div>
		</section>

		<!-- Part 2 -->
		<section class="fade-in">
			<div class="text-center mb-12">
				<h3 class="text-3xl md:text-4xl font-bold">第二部分：浇灌与滋养</h3>
				<p class="text-gray-500 mt-2 text-lg">CONSOLIDATION PHASE: WATERING & NOURISHING</p>
			</div>
			<div class="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
				<!-- Habit 4-5 -->
				<div class="bg-gray-50 p-8 rounded-2xl border border-gray-100 flex flex-col">
					<div class="flex items-center mb-4">
						<i class="fa-solid fa-moon text-3xl gradient-text mr-4"></i>
						<h4 class="text-2xl font-bold">04 睡眠炼金术</h4>
					</div>
					<h5 class="font-bold mb-2">它是什么？</h5>
					<p class="text-gray-600 mb-4 flex-grow">睡眠不是关机，而是大脑进行记忆巩固工作。它会回放、筛选、编码白天的经历，转存为长期记忆。牺牲睡眠学习等于烧掉粮仓。</p>
					<h5 class="font-bold mb-2">如何实践？</h5>
					<ul class="list-disc list-inside text-gray-800 space-y-1">
						<li><strong>规律作息：</strong>每天在同一时间睡觉和起床。</li>
						<li><strong>睡前复习：</strong>睡前10-15分钟快速回顾，给大脑下达“优先处理”指令。</li>
					</ul>
				</div>
				<div class="bg-gray-50 p-8 rounded-2xl border border-gray-100 flex flex-col">
					<div class="flex items-center mb-4">
						<i class="fa-solid fa-person-running text-3xl gradient-text mr-4"></i>
						<h4 class="text-2xl font-bold">05 健脑加油站</h4>
					</div>
					<h5 class="font-bold mb-2">它是什么？</h5>
					<p class="text-gray-600 mb-4 flex-grow">运动和饮食是海马体（记忆中枢）的“健身教练”和“营养师”。有氧运动增加血流，特定营养素是记忆大厦的砖瓦。</p>
					<h5 class="font-bold mb-2">如何实践？</h5>
					<ul class="list-disc list-inside text-gray-800 space-y-1">
						<li><strong>动起来：</strong>每周150分钟有氧运动，哪怕每天散步20分钟。</li>
						<li><strong>聪明地吃：</strong>多摄入Omega-3（鱼、坚果）、抗氧化物（蓝莓、深色蔬菜）。</li>
					</ul>
				</div>
			</div>
		</section>

		<!-- Part 3 -->
		<section class="fade-in">
			<div class="text-center mb-12">
				<h3 class="text-3xl md:text-4xl font-bold">第三部分：收获与修剪</h3>
				<p class="text-gray-500 mt-2 text-lg">RETRIEVAL & OPTIMIZATION: HARVESTING & PRUNING</p>
			</div>
			<div class="space-y-8">
				<!-- Habits 6-10 -->
				<div class="grid md:grid-cols-5 gap-8 items-center bg-gray-50 p-8 rounded-2xl border border-gray-100">
					<div class="md:col-span-2">
						<div class="flex items-center mb-4"><i class="fa-solid fa-repeat text-3xl gradient-text mr-4"></i><h4 class="text-2xl font-bold">06 节拍器回顾法</h4></div>
						<h5 class="font-bold mb-2">它是什么？</h5>
						<p class="text-gray-600">对抗“遗忘曲线”的最好方法，不是一次性长时间学习，而是在即将忘记的临界点，像节拍器一样进行短暂回顾。</p>
					</div>
					<div class="md:col-span-3">
						<h5 class="font-bold mb-2">如何实践？</h5>
						<ul class="list-disc list-inside text-gray-800 space-y-1">
							<li><strong>使用工具：</strong>Anki、Quizlet等App内置了间隔重复算法。</li>
							<li><strong>手动设置：</strong>遵循“1-2-4-7”原则，在第1、2、4、7天后分别回顾。</li>
						</ul>
					</div>
				</div>

				<div class="grid md:grid-cols-5 gap-8 items-center bg-gray-50 p-8 rounded-2xl border border-gray-100">
					<div class="md:col-span-2">
						<div class="flex items-center mb-4"><i class="fa-solid fa-chalkboard-user text-3xl gradient-text mr-4"></i><h4 class="text-2xl font-bold">07 费曼学习法</h4></div>
						<h5 class="font-bold mb-2">它是什么？</h5>
						<p class="text-gray-600">“如果你不能用简单的话把它解释清楚，就说明你还没真正理解。”主动提取并输出，是最高效的记忆方式。</p>
					</div>
					<div class="md:col-span-3">
						<h5 class="font-bold mb-2">如何实践？</h5>
						<ul class="list-disc list-inside text-gray-800 space-y-1">
							<li><strong>教给“小白”：</strong>想象你要把概念教给一个8岁的孩子。卡壳的地方就是你的记忆盲区，回去重新学习。</li>
						</ul>
					</div>
				</div>
				<div class="grid md:grid-cols-5 gap-8 items-center bg-gray-50 p-8 rounded-2xl border border-gray-100">
					<div class="md:col-span-2">
						<div class="flex items-center mb-4"><i class="fa-solid fa-shuffle text-3xl gradient-text mr-4"></i><h4 class="text-2xl font-bold">08 交叉训练法</h4></div>
						<h5 class="font-bold mb-2">它是什么？</h5>
						<p class="text-gray-600">不要长时间只钻研一个主题，而是在相关但不同的主题间切换学习。这会迫使大脑更努力地去提取和分辨。</p>
					</div>
					<div class="md:col-span-3">
						<h5 class="font-bold mb-2">如何实践？</h5>
						<ul class="list-disc list-inside text-gray-800 space-y-1">
							<li><strong>混合练习：</strong>学数学时，把不同类型的题目混在一起练习。学语言时，交替进行阅读、听力和口语练习。</li>
						</ul>
					</div>
				</div>
				<div class="grid md:grid-cols-5 gap-8 items-center bg-gray-50 p-8 rounded-2xl border border-gray-100">
					<div class="md:col-span-2">
						<div class="flex items-center mb-4"><i class="fa-solid fa-leaf text-3xl gradient-text mr-4"></i><h4 class="text-2xl font-bold">09 清理杂草法</h4></div>
						<h5 class="font-bold mb-2">它是什么？</h5>
						<p class="text-gray-600">正念冥想。大脑被各种思绪和焦虑占据，就像花园里长满了杂草。冥想能清除“认知噪音”，为记忆创造空间。</p>
					</div>
					<div class="md:col-span-3">
						<h5 class="font-bold mb-2">如何实践？</h5>
						<ul class="list-disc list-inside text-gray-800 space-y-1">
							<li><strong>每天5分钟：</strong>找个安静地方坐下，闭眼，只关注自己的呼吸。这就像在锻炼你注意力的“肌肉”。</li>
						</ul>
					</div>
				</div>
				<div class="grid md:grid-cols-5 gap-8 items-center bg-gray-50 p-8 rounded-2xl border border-gray-100">
					<div class="md:col-span-2">
						<div class="flex items-center mb-4"><i class="fa-solid fa-server text-3xl gradient-text mr-4"></i><h4 class="text-2xl font-bold">10 建立第二大脑</h4></div>
						<h5 class="font-bold mb-2">它是什么？</h5>
						<p class="text-gray-600">你的大脑不适合储存所有零碎信息。把大脑从繁杂记忆任务中解放出来，交给外部工具，让它专注于思考、连接和创造。</p>
					</div>
					<div class="md:col-span-3">
						<h5 class="font-bold mb-2">如何实践？</h5>
						<ul class="list-disc list-inside text-gray-800 space-y-1">
							<li><strong>善用工具：</strong>使用Notion, Evernote等笔记软件系统地记录。</li>
							<li><strong>核心原则：</strong>不要做信息的搬运工，而是要做加工者。用自己的话总结，建立链接。</li>
						</ul>
					</div>
				</div>

			</div>
		</section>
	</div>

	<!-- Conclusion Section -->
	<footer class="mt-20 md:mt-32 text-center max-w-4xl mx-auto fade-in">
		<h2 class="text-3xl md:text-4xl font-bold">从今天起，成为你记忆花园的主人</h2>
		<p class="text-lg text-gray-600 mt-4 mb-12">记忆力不是天生的魔术，而是一门后天可以习得的园艺。</p>

		<div class="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8 p-6 border-2 border-dashed border-gray-200 rounded-2xl">
			<div class="text-center">
				<p class="text-lg text-red-500 line-through">“我记性不好。”</p>
				<p class="text-xs text-gray-400">一个封闭的、判决式的标签</p>
			</div>
			<i class="fa-solid fa-arrow-right-long text-2xl text-gray-400 hidden md:block"></i>
			<i class="fa-solid fa-arrow-down-long text-2xl text-gray-400 md:hidden"></i>
			<div class="text-center">
				<p class="text-lg text-green-600 font-semibold">“我还没有养成好的记忆习惯。”</p>
				<p class="text-xs text-gray-400">一个开放的、充满可能性的起点</p>
			</div>
		</div>

		<p class="mt-12 text-lg text-gray-700">你不必一次性做到全部。就从这10个习惯中，挑选一个你最心动的开始吧。每一次小小的实践，都是在为你的记忆花园浇水、施肥。久而久之，你会发现，那座曾经荒芜的花园，已经繁花似锦，硕果累累。</p>

		<p class="mt-16 text-2xl md:text-3xl font-bold gradient-text">
			那么，你准备先从哪个习惯开始，为你的人生装上“超频”记忆体？
		</p>
	</footer>

</div>

<script>
	const faders = document.querySelectorAll('.fade-in');

	const appearOptions = {
		threshold: 0.1,
		rootMargin: "0px 0px -50px 0px"
	};

	const appearOnScroll = new IntersectionObserver(function(entries, appearOnScroll) {
		entries.forEach(entry => {
			if (!entry.isIntersecting) {
				return;
			} else {
				entry.target.classList.add('visible');
				appearOnScroll.unobserve(entry.target);
			}
		});
	}, appearOptions);

	faders.forEach(fader => {
		appearOnScroll.observe(fader);
	});
</script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>