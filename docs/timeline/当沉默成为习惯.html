<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>当沉默成为习惯：警惕那只藏在心底的"审查官"</title>
	<script src="https://cdn.tailwindcss.com/3.4.1"></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
	<style>
		@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
		body {
			font-family: 'Noto Sans SC', sans-serif;
			background-color: #FFFFFF;
			color: #000000;
			scroll-behavior: smooth;
		}
		.text-gradient-blue-purple {
			background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
		}
		.text-gradient-orange-red {
			background-image: linear-gradient(to right, #f97316, #ef4444);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
		}
		.bg-gradient-blue-purple {
			background-image: linear-gradient(to right, #3b82f6, #a855f7);
		}
		.bg-gradient-orange-red {
			background-image: linear-gradient(to right, #f97316, #ef4444);
		}
		.highlight-bar {
			display: inline-block;
			width: 80px;
			height: 4px;
			margin-top: 8px;
			margin-bottom: 16px;
		}
		.section-icon {
			font-size: 3rem; /* 48px */
			margin-bottom: 1rem; /* 16px */
			opacity: 0.7;
		}
		.stat-number {
			font-size: 4rem; /* 64px */
			font-weight: 900;
			line-height: 1;
		}
		.emphasis-text {
			font-size: 2.25rem; /* 36px */
			font-weight: 700;
			line-height: 1.3;
		}
		.quote-box {
			padding: 1.5rem;
			border-left-width: 4px;
			margin: 1.5rem 0;
		}
		/* Responsive font sizes */
		@media (min-width: 768px) {
			.emphasis-text {
				font-size: 2.5rem; /* 40px */
			}
			.stat-number {
				font-size: 5rem; /* 80px */
			}
		}
		@media (min-width: 1024px) {
			.emphasis-text {
				font-size: 3rem; /* 48px */
			}
			.stat-number {
				font-size: 6rem; /* 96px */
			}
		}
		.decorative-line {
			width: 100%;
			height: 2px;
			background: linear-gradient(to right, rgba(59,130,246,0.5) 0%, rgba(168,85,247,0.5) 50%, rgba(249,115,22,0.5) 100%);
			margin: 3rem 0;
		}
		.glow-effect {
			position: absolute;
			border-radius: 50%;
			filter: blur(50px);
			z-index: -1;
			opacity: 0.3;
		}
		.glow-blue {
			width: 300px;
			height: 300px;
			background-color: #3b82f6;
			top: 10%;
			left: 5%;
		}
		.glow-purple {
			width: 250px;
			height: 250px;
			background-color: #8b5cf6;
			bottom: 15%;
			right: 10%;
		}
		 .glow-orange {
			width: 200px;
			height: 200px;
			background-color: #f97316;
			top: 40%;
			right: 20%;
		}
	</style>
</head>
<body class="overflow-x-hidden">
<div class="relative min-h-screen">
	<!-- Background Glows -->
	<div class="glow-effect glow-blue hidden md:block"></div>
	<div class="glow-effect glow-purple hidden md:block"></div>
	<div class="glow-effect glow-orange hidden md:block"></div>

	<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-20 relative z-10">

		<!-- Header Section -->
		<header class="text-center mb-16 md:mb-24">
			<h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold mb-4">
				当沉默成为<span class="text-gradient-blue-purple">习惯</span>
			</h1>
			<p class="text-2xl sm:text-3xl md:text-4xl font-bold mb-6">警惕那只藏在心底的"<span class="text-gradient-orange-red">审查官</span>"</p>
			<p class="text-sm uppercase text-gray-500 tracking-widest mb-8">THE INVISIBLE CENSOR WITHIN</p>
			<div class="max-w-3xl mx-auto text-lg md:text-xl text-gray-700 leading-relaxed">
				<p class="mb-4">清晨的鸟鸣从不思考自己是否聒噪，<i class="fas fa-feather-alt text-blue-400 mx-1"></i> 山涧的溪流不会怀疑自己的流向是否得体。<i class="fas fa-water text-purple-400 mx-1"></i></p>
				<p>而我们——这颗星球上最擅长表达的物种，却在日复一日地练习沉默的艺术。</p>
			</div>
		</header>

		<!-- Core Warning Section -->
		<section class="my-16 md:my-24 text-center">
			<p class="emphasis-text max-w-4xl mx-auto">
				每一次<strong class="text-gradient-orange-red">欲言又止</strong>，都是对灵魂的一次微小<strong class="text-gradient-blue-purple">背叛</strong>。
			</p>
			<p class="text-lg md:text-xl text-gray-600 mt-6 max-w-2xl mx-auto">
				那些被咽回去的真心话，那些被修饰过的情绪，最终都会在我们的精神世界堆积成一座沉默的坟场。你可知？最可怕的审查从不是来自外界的禁令，而是那个已经内化到骨髓里的"<strong class="font-bold">自我审查机制</strong>"——它正以温柔的姿态，慢性谋杀着你的生命力。
			</p>
			<div class="mt-10 flex justify-center">
				<i class="fas fa-user-secret section-icon text-gradient-blue-purple"></i>
			</div>
		</section>

		<div class="decorative-line"></div>

		<!-- Section 1: 温水煮青蛙 -->
		<section class="my-16 md:my-24">
			<div class="lg:flex lg:items-center lg:gap-12">
				<div class="lg:w-1/2 text-center lg:text-left mb-8 lg:mb-0">
					<i class="fas fa-brain section-icon text-gradient-blue-purple"></i>
					<h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-2">温水煮青蛙式</h2>
					<p class="text-2xl sm:text-3xl font-semibold text-gradient-blue-purple mb-3">精神阉割</p>
					<p class="text-xs uppercase text-gray-400 tracking-widest mb-6">THE SLOW EROSION</p>
					<p class="text-gray-700 leading-relaxed">
						我们的大脑里住着一位看不见的"审查官"。它会在每个表达欲萌动的时刻轻声耳语：
					</p>
					<div class="quote-box border-l-4 border-blue-500 bg-blue-50 my-6">
						<p class="italic text-gray-600">"这些琐事也值得说？"</p>
						<p class="italic text-gray-600">"你的感受太矫情了。"</p>
						<p class="italic text-gray-600">"别人会怎么想？"</p>
						<p class="italic text-gray-600">"算了吧，说了也没用..."</p>
					</div>
					<p class="text-gray-700 leading-relaxed">
						这些看似"理性"的劝阻，实则是精神上的自我截肢。
					</p>
				</div>
				<div class="lg:w-1/2">
					<div class="p-6 md:p-8 rounded-lg shadow-2xl bg-white relative overflow-hidden">
						<div class="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full opacity-30 animate-pulse"></div>
						<div class="absolute -bottom-10 -right-10 w-24 h-24 bg-gradient-to-tl from-orange-500 to-pink-500 rounded-full opacity-30 animate-pulse delay-500"></div>
						<p class="emphasis-text mb-4">当表达成为<strong class="text-gradient-blue-purple">勇气</strong>的事</p>
						<p class="text-xl md:text-2xl font-semibold text-gray-800 mb-6">我们已输掉作为人的基本<strong class="text-gradient-orange-red">尊严</strong>。</p>
						<div class="flex items-center text-sm text-gray-600">
							<i class="fas fa-chart-line text-blue-500 mr-3 text-2xl"></i>
							<span>心理学研究显示，长期自我压抑会导致大脑前额叶与杏仁核的连接异常——我们不仅在压抑话语，更是在重塑自己的大脑结构。</span>
						</div>
					</div>
				</div>
			</div>
		</section>

		<div class="decorative-line"></div>

		<!-- Section 2: 失语症患者 -->
		<section class="my-16 md:my-24">
			<div class="lg:flex lg:items-center lg:gap-12 flex-row-reverse">
				<div class="lg:w-1/2 text-center lg:text-left mb-8 lg:mb-0">
					<i class="fas fa-comment-slash section-icon text-gradient-orange-red"></i>
					<h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-2">失语症患者的</h2>
					<p class="text-2xl sm:text-3xl font-semibold text-gradient-orange-red mb-3">生存困境</p>
					<p class="text-xs uppercase text-gray-400 tracking-widest mb-6">THE VOICELESS EXISTENCE</p>
					<p class="text-gray-700 leading-relaxed mb-4">
						观察那些习惯性沉默的人，你会发现一个可怕的共同点：他们的眼睛逐渐失去光彩。这不是比喻——长期自我审查会导致：
					</p>
					<ul class="space-y-3 text-gray-700 mb-6">
						<li class="flex items-start"><i class="fas fa-circle-notch fa-spin text-purple-500 mr-3 mt-1 text-lg"></i><span><strong>情感麻木：</strong>分不清是"不想说"还是"不会说"</span></li>
						<li class="flex items-start"><i class="fas fa-puzzle-piece text-blue-500 mr-3 mt-1 text-lg"></i><span><strong>认知萎缩：</strong>思维因缺乏语言锤炼而日渐僵化</span></li>
						<li class="flex items-start"><i class="fas fa-users-slash text-orange-500 mr-3 mt-1 text-lg"></i><span><strong>关系荒漠化：</strong>所有交流都停留在安全而空洞的层面</span></li>
					</ul>
					<p class="text-gray-700 leading-relaxed">
						就像被反复修剪的盆栽，我们把自己修剪成"得体"的形状，却忘了植物本可以长成参天大树。
					</p>
				</div>
				<div class="lg:w-1/2">
					<div class="p-6 md:p-8 rounded-lg shadow-2xl bg-white relative text-center">
						<div class="mx-auto w-32 h-32 md:w-40 md:h-40 mb-6">
							<svg class="w-full h-full text-gray-300" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M50 10C27.9086 10 10 27.9086 10 50C10 72.0914 27.9086 90 50 90C72.0914 90 90 72.0914 90 50C90 27.9086 72.0914 10 50 10ZM50 82C32.3269 82 18 67.6731 18 50C18 32.3269 32.3269 18 50 18C67.6731 18 82 32.3269 82 50C82 67.6731 67.6731 82 50 82Z" fill="currentColor"/>
								<path d="M65 42H35C33.3431 42 32 43.3431 32 45V55C32 56.6569 33.3431 58 35 58H65C66.6569 58 68 56.6569 68 55V45C68 43.3431 66.6569 42 65 42Z" fill="url(#paint0_linear_body)" opacity="0.7"/>
								<path d="M50 58V70M50 70L45 65M50 70L55 65" stroke="url(#paint1_linear_body)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
								<defs>
									<linearGradient id="paint0_linear_body" x1="32" y1="50" x2="68" y2="50" gradientUnits="userSpaceOnUse">
										<stop stop-color="#f97316"/>
										<stop offset="1" stop-color="#ef4444"/>
									</linearGradient>
									<linearGradient id="paint1_linear_body" x1="50" y1="58" x2="50" y2="70" gradientUnits="userSpaceOnUse">
										<stop stop-color="#f97316"/>
										<stop offset="1" stop-color="#ef4444" stop-opacity="0.5"/>
									</linearGradient>
								</defs>
							</svg>
						</div>
						<p class="emphasis-text mb-4">沉默真的会<strong class="text-gradient-orange-red">生病</strong></p>
						<p class="text-xl md:text-2xl font-semibold text-gray-800">那些没说出口的话，最终都会变成<strong class="text-gradient-blue-purple">内伤</strong>在身体里游走，临床医学称之为"<strong class="font-semibold">躯体化障碍</strong>"。</p>
					</div>
				</div>
			</div>
		</section>

		<div class="decorative-line"></div>

		<!-- Section 3: 打破沉默 -->
		<section class="my-16 md:my-24">
			<div class="text-center mb-12">
				<i class="fas fa-bullhorn section-icon text-gradient-blue-purple"></i>
				<h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-2">打破沉默的<span class="text-gradient-blue-purple">螺旋</span></h2>
				<p class="text-xs uppercase text-gray-400 tracking-widest mb-6">RECLAIMING YOUR VOICE</p>
				<p class="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto">重获表达自由需要一场精密的自我革命：</p>
			</div>

			<div class="grid md:grid-cols-3 gap-8 md:gap-12">
				<div class="p-6 rounded-lg shadow-xl bg-white hover:shadow-2xl transition-shadow duration-300 relative overflow-hidden">
					<div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-600"></div>
					<div class="flex items-center mb-4">
						<i class="fas fa-pencil-alt text-blue-500 text-3xl mr-4"></i>
						<h3 class="text-xl font-bold">建立表达仪式</h3>
					</div>
					<p class="text-gray-600 text-sm">CREATE RITUALS</p>
					<p class="text-gray-700 mt-2">每天记录三件"微不足道"的感受。</p>
				</div>
				<div class="p-6 rounded-lg shadow-xl bg-white hover:shadow-2xl transition-shadow duration-300 relative overflow-hidden">
					<div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
					<div class="flex items-center mb-4">
						<i class="fas fa-shield-alt text-purple-500 text-3xl mr-4"></i>
						<h3 class="text-xl font-bold">寻找安全试验场</h3>
					</div>
					<p class="text-gray-600 text-sm">FIND SAFE SPACES</p>
					<p class="text-gray-700 mt-2">从树洞日记到小众社群，重建表达信心。</p>
				</div>
				<div class="p-6 rounded-lg shadow-xl bg-white hover:shadow-2xl transition-shadow duration-300 relative overflow-hidden">
					<div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 to-orange-500"></div>
					<div class="flex items-center mb-4">
						<i class="fas fa-lightbulb text-orange-500 text-3xl mr-4"></i>
						<h3 class="text-xl font-bold">练习"不完美表达"</h3>
					</div>
					<p class="text-gray-600 text-sm">PRACTICE IMPERFECTION</p>
					<p class="text-gray-700 mt-2">故意说些"蠢话"，打破完美主义魔咒。</p>
				</div>
			</div>

			<div class="mt-16 text-center">
				<p class="emphasis-text max-w-4xl mx-auto">
					<strong class="text-gradient-blue-purple">表达</strong>不是特权，而是生命<strong class="text-gradient-orange-red">与生俱来</strong>的权利。
				</p>
				<p class="text-lg md:text-xl text-gray-600 mt-6 max-w-3xl mx-auto">
					当你开始真实发声，会发现一个惊人的事实：那些你以为会嘲笑你的人，其实也在暗自期待有人打破沉默的共谋。
				</p>
			</div>
		</section>

		<div class="decorative-line"></div>

		<!-- Footer / Conclusion -->
		<footer class="mt-16 md:mt-24 text-center">
			<div class="max-w-3xl mx-auto">
				<i class="fas fa-volume-up text-5xl mb-6 text-gradient-blue-purple"></i>
				<p class="text-xl md:text-2xl lg:text-3xl font-bold text-gray-800 mb-6">
					在这个信息爆炸却真情匮乏的时代，你的声音可能正是某个人等待已久的<strong class="text-gradient-blue-purple">救赎</strong>。
				</p>
				<p class="text-lg md:text-xl text-gray-700 mb-8">
					下一次当内心的审查官又开始耳语时，请记得：溪流因为流动才清澈，生命因为表达才鲜活。
				</p>
				<p class="text-3xl sm:text-4xl md:text-5xl font-extrabold">
					沉默不是金，而是最昂贵的<span class="text-gradient-orange-red">自我囚禁</span>。
				</p>
				<p class="text-sm uppercase text-gray-500 tracking-widest mt-4">YOUR VOICE MATTERS</p>
			</div>
			<div class="mt-12 text-xs text-gray-400">
				Generated by Dynamic Web Page Assistant
			</div>
		</footer>

	</div>
</div>
<script>
	// Optional: Simple JS for subtle animations or interactions if needed
	// Example: Parallax for glows (more complex, not implemented here for brevity)
	// document.addEventListener('scroll', function() {
	//     const glows = document.querySelectorAll('.glow-effect');
	//     const scrollY = window.scrollY;
	//     glows.forEach(glow => {
	//         const speed = parseFloat(glow.dataset.speed || 0.1);
	//         glow.style.transform = `translateY(${scrollY * speed}px)`;
	//     });
	// });
</script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>