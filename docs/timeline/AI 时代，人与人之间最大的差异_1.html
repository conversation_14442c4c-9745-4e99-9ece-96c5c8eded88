<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 时代，人与人之间最大的差异</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.3"></script>
    
    <!-- Font Awesome (for icons) via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- Chart.js via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 自定义字体和全局样式 */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5; /* 极浅的灰色背景 */
            background-image: 
                radial-gradient(circle at 1% 1%, hsla(212, 96%, 89%, 0.5) 0px, transparent 50%),
                radial-gradient(circle at 99% 1%, hsla(333, 86%, 82%, 0.5) 0px, transparent 50%),
                radial-gradient(circle at 1% 99%, hsla(149, 83%, 85%, 0.5) 0px, transparent 50%),
                radial-gradient(circle at 99% 99%, hsla(50, 95%, 80%, 0.5) 0px, transparent 50%);
            color: #1d1d1f; /* 近黑色文本 */
            overflow-x: hidden;
        }

        /* 核心标题的全息彩虹渐变 */
        .holographic-text {
            background-image: linear-gradient(90deg, #3B82F6, #8B5CF6, #EC4899, #F59E0B);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        /* 动画效果：滚动进入时触发 */
        .reveal {
            opacity: 0;
            transform: translateY(40px);
            transition: opacity 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transition-delay: 0.1s;
        }

        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Chart.js 图表容器需要明确的宽高 */
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }

        /* 勾线风格图标 */
        .icon-outline {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 4rem;
            height: 4rem;
            border-radius: 1.5rem;
            border: 2px solid currentColor;
        }
        .icon-outline i {
            font-size: 1.75rem;
        }

        /* 卡片使用更宽的圆角 */
        .card {
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 2.5rem; /* 苹果风格的宽圆角 */
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="antialiased">

    <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-20 md:py-32">

        <!-- Section 1: Hero Title -->
        <header class="text-center mb-24 md:mb-32 reveal">
            <p class="text-lg md:text-xl text-gray-500 font-medium tracking-wider mb-4">A Question for the New Era</p>
            <h1 class="text-5xl md:text-7xl lg:text-8xl font-black tracking-tighter leading-tight">
                在<span class="holographic-text">AI巨变</span>的时代<br>人与人之间最大的差异是<span class="text-blue-500">什么？</span>
            </h1>
            <p class="mt-8 text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto">
                这是一个直击时代灵魂的终极问题。
            </p>
        </header>

        <!-- Section 2: Core Thesis -->
        <section class="mb-20 md:mb-28 reveal">
            <div class="card p-8 md:p-12 lg:p-16">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="text-center lg:text-left">
                        <p class="text-lg text-gray-500">The Fundamental Shift</p>
                        <h2 class="text-4xl md:text-5xl font-bold mt-2">
                            从 <span class="text-red-500">“你拥有什么”</span><br>深刻地转向<br><span class="text-green-500">“你是谁”</span>
                        </h2>
                        <p class="mt-6 text-lg text-gray-600">
                            当知识和智力唾手可得，传统标尺正在崩塌。差异不再是你大脑这块“硬盘”的容量和速度，而是关于你这整个操作系统的内核、驱动与接口。
                        </p>
                    </div>
                    <div class="flex items-center justify-center space-x-8">
                        <!-- Giant Visual Element Contrast -->
                        <div class="text-center">
                            <i class="fa-solid fa-brain text-8xl md:text-9xl text-gray-300"></i>
                            <p class="mt-4 font-bold text-gray-400">What you have</p>
                        </div>
                        <i class="fa-solid fa-arrow-right-long text-5xl text-gray-400"></i>
                        <div class="text-center">
                             <div class="relative inline-block">
                                <i class="fa-solid fa-heart text-8xl md:text-9xl text-green-500"></i>
                                <div class="absolute inset-0 bg-green-400 opacity-30 mix-blend-multiply blur-2xl -z-10"></div>
                            </div>
                            <p class="mt-4 font-bold text-green-600">Who you are</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: The 4 Dimensions Grid -->
        <main class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-10">

            <!-- Card 1: Questions -->
            <div class="card p-8 lg:p-10 flex flex-col reveal">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <p class="font-semibold text-blue-500">维度 01</p>
                        <h3 class="text-2xl lg:text-3xl font-bold mt-1">提问的深度与角度</h3>
                        <p class="text-gray-500 mt-2">从“答案的消费者”到“问题的创造者”</p>
                    </div>
                     <div class="icon-outline text-blue-500 flex-shrink-0">
                        <i class="fa-solid fa-question"></i>
                    </div>
                </div>
                <div class="mt-6 text-gray-700 space-y-4 text-base/relaxed flex-grow">
                    <p>当AI能提供无穷答案时，价值在于提出那个开启全新可能性的问题。AI是被动的，其力量取决于你的提问。</p>
                    <ul class="list-none space-y-3 pt-2">
                        <li class="flex items-start"><i class="fa-regular fa-circle-check text-blue-500 mt-1 mr-3"></i><div><strong class="text-black">好奇心：</strong>不满足于首个答案，追问十个“为什么”？</div></li>
                        <li class="flex items-start"><i class="fa-regular fa-circle-check text-blue-500 mt-1 mr-3"></i><div><strong class="text-black">洞察力：</strong>是问如何改进马车，还是想象无需马的交通工具？</div></li>
                        <li class="flex items-start"><i class="fa-regular fa-circle-check text-blue-500 mt-1 mr-3"></i><div><strong class="text-black">独特性：</strong>当众人都问“如何提效”时，你是否敢问“这事值得做吗”？</div></li>
                    </ul>
                </div>
            </div>

            <!-- Card 2: Action -->
            <div class="card p-8 lg:p-10 flex flex-col reveal">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <p class="font-semibold" style="color: #10B981;">维度 02</p>
                        <h3 class="text-2xl lg:text-3xl font-bold mt-1">行动的勇气与韧性</h3>
                        <p class="text-gray-500 mt-2">从“完美的规划者”到“笨拙的践行者”</p>
                    </div>
                     <div class="icon-outline text-green-500 flex-shrink-0">
                        <i class="fa-solid fa-person-running"></i>
                    </div>
                </div>
                <div class="mt-6 text-gray-700 space-y-4 text-base/relaxed flex-grow">
                   <p>AI能生成完美计划，但这放大了知与行之间的鸿沟。当人人都有“最优解”，稀缺品变成了实干。</p>
                   <div class="text-center my-6 py-4 border-y-2 border-dashed border-gray-300">
                        <p class="text-3xl md:text-5xl font-black text-green-600 tracking-tighter">真刀真枪</p>
                        <p class="text-sm font-medium text-gray-500 tracking-widest mt-1">ACTION OVER PLANNING</p>
                   </div>
                   <p>真正的领先者，是那些敢于迈出第一步、能从失败中迭代、并为不确定性后果负责的人。</p>
                </div>
            </div>

            <!-- Card 3: Purpose (Why) -->
            <div class="card p-8 lg:p-10 flex flex-col reveal">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <p class="font-semibold text-purple-500">维度 03</p>
                        <h3 class="text-2xl lg:text-3xl font-bold mt-1">目标的感召力与“为何”</h3>
                        <p class="text-gray-500 mt-2">从“高效的执行者”到“有使命的领导者”</p>
                    </div>
                    <div class="icon-outline text-purple-500 flex-shrink-0">
                        <i class="fa-solid fa-bullseye"></i>
                    </div>
                </div>
                <div class="mt-6 text-gray-700 space-y-4 text-base/relaxed flex-grow flex flex-col">
                    <p>AI是终极的“How”工具，但无法提供你内心的“Why”。当“术”变得廉价，“道”就成了一切价值的源头。</p>
                    <!-- Chart.js Canvas -->
                    <div class="chart-container mt-auto pt-4">
                        <canvas id="whyChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Card 4: Experience & Love -->
            <div class="card p-8 lg:p-10 flex flex-col reveal">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <p class="font-semibold text-orange-500">维度 04</p>
                        <h3 class="text-2xl lg:text-3xl font-bold mt-1">体验的质感与爱的能力</h3>
                        <p class="text-gray-500 mt-2">从“信息的处理器”到“意义的连接器”</p>
                    </div>
                    <div class="icon-outline text-orange-500 flex-shrink-0">
                        <i class="fa-solid fa-hand-holding-heart"></i>
                    </div>
                </div>
                <div class="mt-6 text-gray-700 space-y-4 text-base/relaxed flex-grow">
                    <p>这是最本质的差异。AI没有肉身体验和真正同理心。在智力平权的时代，那些反“智力”的、属于人性的维度，将变得无价。</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-4 text-center mt-6">
                        <div class="p-3 bg-orange-100/50 rounded-2xl"><i class="fa-solid fa-palette text-orange-500 mr-2"></i>审美与品味</div>
                        <div class="p-3 bg-yellow-100/50 rounded-2xl"><i class="fa-solid fa-face-laugh-beam text-yellow-500 mr-2"></i>幽默与自嘲</div>
                        <div class="p-3 bg-pink-100/50 rounded-2xl"><i class="fa-solid fa-users text-pink-500 mr-2"></i>信任与领导力</div>
                        <div class="p-3 bg-red-100/50 rounded-2xl col-span-2 sm:col-span-3"><i class="fa-solid fa-heart text-red-500 mr-2"></i>爱与被爱的能力：建立深度、真实的人际连接</div>
                    </div>
                </div>
            </div>

        </main>

        <!-- Section 4: Conclusion -->
        <section class="mt-20 md:mt-28 reveal">
            <div class="bg-gray-900 text-white p-12 md:p-16 text-center card">
                <p class="text-lg md:text-xl font-medium text-gray-400">Conclusion</p>
                <h2 class="text-4xl md:text-6xl font-black tracking-tight mt-4">
                    最大的差异，不再是知识的鸿沟<br>而是<span class="holographic-text">品格的鸿沟</span>
                </h2>
                <p class="mt-8 text-lg text-gray-300 max-w-4xl mx-auto">
                    AI给了我们通往“知识巅峰”的梯子，但你的人生能走多远，取决于你内心的<strong class="text-white">好奇、勇气、使命和爱的能力</strong>。
                </p>
                <div class="mt-12 border-t border-gray-700 pt-8">
                     <p class="text-2xl md:text-3xl font-bold">
                        所以，AI时代最重要的问题，不是去问AI。
                     </p>
                     <p class="text-2xl md:text-3xl font-bold text-blue-400 mt-2">
                        而是要不断地，问我们自己。
                     </p>
                </div>
            </div>
        </section>

    </div>

    <footer class="text-center py-8 text-gray-500 text-sm">
        <p>A Visual Interpretation based on your insights. Designed in Hybrid Grid Style.</p>
    </footer>


    <script>
        // JavaScript for Scroll Reveal Animation
        const revealElements = document.querySelectorAll('.reveal');

        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    // Optional: stop observing once it's visible
                    revealObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1 // Trigger when 10% of the element is visible
        });

        revealElements.forEach(el => {
            revealObserver.observe(el);
        });


        // JavaScript for Chart.js
        document.addEventListener('DOMContentLoaded', () => {
            const ctx = document.getElementById('whyChart').getContext('2d');
            
            // 使用高亮色自身透明度渐变
            const purpleGradient = ctx.createLinearGradient(0, 0, 0, 250);
            purpleGradient.addColorStop(0, 'rgba(168, 85, 247, 0.7)'); // purple-500 with alpha
            purpleGradient.addColorStop(1, 'rgba(168, 85, 247, 0.1)');

            const orangeGradient = ctx.createLinearGradient(0, 0, 0, 250);
            orangeGradient.addColorStop(0, 'rgba(249, 115, 22, 0.7)'); // orange-500 with alpha
            orangeGradient.addColorStop(1, 'rgba(249, 115, 22, 0.1)');

            const whyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['欲望驱动', '热爱驱动', '服务小我', '服务大我'],
                    datasets: [{
                        label: '驱动力来源',
                        data: [65, 90, 50, 85], // Sample data
                        backgroundColor: [
                            orangeGradient, // 淡橙色 for "欲望"
                            purpleGradient, // 强调色 for "热爱"
                            orangeGradient, // 淡橙色 for "小我"
                            purpleGradient, // 强调色 for "大我"
                        ],
                        borderColor: [
                            'rgba(249, 115, 22, 1)',
                            'rgba(168, 85, 247, 1)',
                            'rgba(249, 115, 22, 1)',
                            'rgba(168, 85, 247, 1)',
                        ],
                        borderWidth: 0,
                        borderRadius: 8,
                        barPercentage: 0.6,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: '#1d1d1f',
                            titleFont: { family: "'Noto Sans SC', sans-serif", weight: 'bold' },
                            bodyFont: { family: "'Noto Sans SC', sans-serif" },
                            padding: 10,
                            cornerRadius: 8,
                            displayColors: false,
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false,
                                color: 'rgba(0, 0, 0, 0.05)',
                            },
                            ticks: {
                                padding: 10,
                                font: {
                                    family: "'Noto Sans SC', sans-serif"
                                }
                            }
                        },
                        x: {
                             grid: {
                                display: false,
                             },
                             ticks: {
                                padding: 10,
                                font: {
                                    family: "'Noto Sans SC', sans-serif",
                                    size: 14,
                                    weight: '500'
                                }
                             }
                        }
                    }
                }
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>