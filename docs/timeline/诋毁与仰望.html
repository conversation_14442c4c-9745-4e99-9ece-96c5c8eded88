<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诋毁与仰望</title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', 'Helvetica Neue', Arial, sans-serif;
            background-color: #DCD6CC; /* 褪色的亚麻色 */
            /* Slightly more textured background */
            background-image: linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%, rgba(0,0,0,0.01)),
                              linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%, rgba(0,0,0,0.01));
            background-size: 30px 30px;
            background-position: 0 0, 15px 15px;
            color: #4A3B31; /* 更深的陶土色，近乎褪色的墨黑 */
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .font-display {
            font-weight: 900;
            line-height: 1.05; /* Adjusted for stacking */
            letter-spacing: -0.03em;
        }
        .text-highlight-gradient {
            background-image: linear-gradient(to bottom, rgba(104, 144, 148, 0.8) 0%, rgba(104, 144, 148, 0.4) 70%, rgba(104, 144, 148, 0.15) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            padding-bottom: 0.1em; /* Ensure gradient covers descenders */
        }
        .text-connecting {
            font-size: clamp(1.5rem, 5vw, 3.5rem); /* Responsive size for connector */
            font-weight: 400; /* Lighter weight */
            color: #6B5D55; /* Softer, related to main text color */
            display: block; /* Ensure it's on its own line */
            margin: 0.3em 0; /* Vertical spacing */
            letter-spacing: normal;
        }
        .text-colossus {
            font-size: clamp(4rem, 18vw, 15rem); /* Even larger for key words */
        }

        .memphis-accent {
            position: absolute;
            opacity: 0.05;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes slowDrift {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(5px, -8px) rotate(1deg); }
            75% { transform: translate(-5px, 8px) rotate(-1deg); }
        }
        .animate-slow-drift {
            animation: slowDrift 15s ease-in-out infinite;
        }
    </style>
</head>
<body class="p-4 md:p-6 selection:bg-[#689094]/30 selection:text-[#4A3B31]">

    <!-- Subtle Memphis Accents -->
    <div class="memphis-accent w-40 h-1 bg-[#B08D57] top-[15%] left-[10%] transform -rotate-[30deg] animate-slow-drift" style="animation-delay: -2s;"></div>
    <div class="memphis-accent w-2 h-32 bg-[#C8BFB6] bottom-[20%] right-[12%] transform rotate-[20deg] animate-slow-drift" style="animation-delay: -5s;"></div>
    <div class="memphis-accent w-24 h-24 border-2 border-[#689094] rounded-full top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 opacity-[0.03]"></div>


    <main class="relative z-10">
        <h1 class="font-display text-colossus text-[#3D3D3D]">
            诋毁
        </h1>
        <span class="text-connecting">本身就是一种</span>
        <h2 class="font-display text-colossus">
            <span class="text-highlight-gradient">仰望</span>
        </h2>

        <p class="mt-10 md:mt-16 text-base md:text-lg text-[#706158] uppercase tracking-wider">
            SLANDER IS ITSELF A FORM OF ADMIRATION
        </p>

        <!-- A simple line art element as a divider/focus point -->
        <div class="mx-auto mt-8 md:mt-12 w-1/3 max-w-[100px] h-0.5 bg-[#B08D57] opacity-40"></div>

    </main>

    <footer class="absolute bottom-4 md:bottom-6 text-center w-full z-10">
        <p class="text-xs text-[#84756C] opacity-80">An insight into perception.</p>
    </footer>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>