<!DOCTYPE html>
<html lang="zh-CN" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一个父亲的“反常识”英语启蒙宣言</title>
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --system-blue: 0 122 255;
            --action-green: 52 199 89;
            --knowledge-purple: 88 86 214;
            --aurora-gradient: linear-gradient(90deg, #0ea5e9, #22d3ee, #6366f1);
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: #f9fafb;
            color: #111827;
        }

        html.dark body {
            background-color: #0d1117;
            color: #f9fafb;
        }

        .text-aurora {
            background: var(--aurora-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.7s cubic-bezier(0.33, 1, 0.68, 1), transform 0.7s cubic-bezier(0.33, 1, 0.68, 1);
        }

        .is-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 { transition-delay: 100ms; }
        .delay-2 { transition-delay: 200ms; }
        .delay-3 { transition-delay: 300ms; }
        .delay-4 { transition-delay: 400ms; }

        .dark .card-glass {
            background-color: rgba(28, 41, 60, 0.5);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="antialiased">

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32 2xl:max-w-screen-2xl">

        <!-- Hero Section -->
        <section class="text-center mb-24 md:mb-32">
            <div class="fade-in-up flex justify-center items-center gap-4 md:gap-8">
                <div class="text-center">
                    <p class="text-7xl md:text-9xl font-black text-gray-900 dark:text-white">11<span class="text-aurora">岁</span></p>
                    <p class="text-sm md:text-base text-gray-500 dark:text-gray-400 mt-1">AGE</p>
                </div>
                <div class="text-center">
                    <p class="text-7xl md:text-9xl font-black text-gray-900 dark:text-white">C1</p>
                    <p class="text-sm md:text-base text-gray-500 dark:text-gray-400 mt-1">CEFR LEVEL</p>
                </div>
                <div class="text-center">
                    <p class="text-7xl md:text-9xl font-black text-gray-900 dark:text-white">~6.0</p>
                    <p class="text-sm md:text-base text-gray-500 dark:text-gray-400 mt-1">IELTS SCORE</p>
                </div>
            </div>
            <h1 class="fade-in-up delay-1 mt-12 text-4xl md:text-6xl font-bold tracking-tighter text-gray-800 dark:text-gray-200">
                一个父亲的<span class="text-aurora">“反常识”</span><br>英语启蒙宣言
            </h1>
            <p class="fade-in-up delay-2 mt-6 text-lg md:text-xl max-w-3xl mx-auto text-gray-600 dark:text-gray-400">
                这一切是如何发生的？答案藏在三个“反常识”的100倍定律里。
            </p>
        </section>

        <!-- The 100x Laws Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            
            <!-- Law 1: Immersion -->
            <div class="fade-in-up delay-1 relative bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col overflow-hidden">
                <span class="absolute -top-8 -right-8 text-[12rem] font-black text-gray-500/5 dark:text-white/5 select-none">100X</span>
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50">
                    <i class="fa-solid fa-headphones-simple text-3xl" style="color: rgb(var(--system-blue));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">沉浸式“听看”</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">比 苦读文本 重要100倍</p>
                <p class="mt-6 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    语言的本质是声音。我们彻底抛弃“中英翻译”的陈旧地图，直接把孩子扔进原声的“Minecraft宇宙”。认知负担越轻，吸收效率越高。
                </p>
            </div>

            <!-- Law 2: Fun -->
            <div class="fade-in-up delay-2 relative bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col overflow-hidden">
                <span class="absolute -top-8 -right-8 text-[12rem] font-black text-gray-500/5 dark:text-white/5 select-none">100X</span>
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/50 dark:to-green-800/50">
                     <i class="fa-solid fa-rocket text-3xl" style="color: rgb(var(--action-green));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">无法抑制的“乐趣”</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">比 精心指导 重要100倍</p>
                 <p class="mt-6 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    我做的最正确的事，就是将她对游戏的痴迷，转化为英语学习的核动力。我没有教她英语，我只是为她的热爱，找到了一个英文的出口。
                </p>
            </div>

            <!-- Law 3: Knowledge -->
            <div class="fade-in-up delay-3 relative bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col overflow-hidden">
                <span class="absolute -top-8 -right-8 text-[12rem] font-black text-gray-500/5 dark:text-white/5 select-none">100X</span>
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/50 dark:to-purple-800/50">
                     <i class="fa-solid fa-globe-americas text-3xl" style="color: rgb(var(--knowledge-purple));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">开阔的“见识”</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">比 语言本身 重要100倍</p>
                 <p class="mt-6 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    我带她读《国家地理》，是为了让她看到一个更有趣的世界。当她对世界充满好奇，会自发地去驾驭语言这艘船，去往更远的地方。语言是工具，求知欲才是引擎。
                </p>
            </div>
        </div>

        <!-- Reflection Section -->
        <section class="mt-24 md:mt-32 fade-in-up">
            <h2 class="text-center text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-200">同样深刻的教训</h2>
            <div class="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
                <blockquote class="bg-gray-100 dark:bg-gray-800/50 border-l-4 border-red-500 p-6 rounded-r-lg">
                    <h3 class="text-xl font-semibold text-red-600 dark:text-red-400 flex items-center"><i class="fa-solid fa-circle-xmark mr-2"></i>最大的错误：寄望于体制</h3>
                    <p class="mt-2 text-gray-700 dark:text-gray-300">我错了。学校的教学法，依然停留在30年前，它在批量制造“哑巴英语”，而不是培养一个能用语言探索世界的孩子。</p>
                </blockquote>
                <blockquote class="bg-gray-100 dark:bg-gray-800/50 border-l-4 border-yellow-500 p-6 rounded-r-lg">
                    <h3 class="text-xl font-semibold text-yellow-600 dark:text-yellow-400 flex items-center"><i class="fa-solid fa-hourglass-half mr-2"></i>最大的遗憾：醒悟得太晚</h3>
                    <p class="mt-2 text-gray-700 dark:text-gray-300">如果从2-3岁就让她在英文动画片的海洋里“玩耍”，她7-8岁时，早已是双语自由的“原住民”。</p>
                </blockquote>
            </div>
        </section>


        <!-- Conclusion Section -->
        <section class="text-center mt-24 md:mt-32 fade-in-up">
            <h2 class="text-4xl md:text-6xl lg:text-7xl font-black tracking-tight text-gray-800 dark:text-gray-200 leading-tight">
                请把英语，从一门“学科”<br>还原成一种<span class="text-aurora">“生活方式”</span>
            </h2>
            <p class="mt-8 text-lg md:text-xl max-w-3xl mx-auto text-gray-600 dark:text-gray-400">
                忘掉单词表和语法书。找到孩子的兴趣燃点，然后用海量的、原汁原味的视听资源，把火烧旺。他们会在玩耍中，顺便征服一门语言。
            </p>
            <div class="mt-16">
                 <div class="w-20 h-20 mx-auto mb-6 rounded-3xl flex items-center justify-center bg-gradient-to-br from-orange-100 to-red-200 dark:from-orange-900/50 dark:to-red-800/50">
                    <i class="fa-solid fa-fire-flame-curved text-4xl text-orange-500"></i>
                </div>
                <p class="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    我们的角色，不是教师
                </p>
                <p class="mt-2 text-2xl md:text-3xl font-bold text-gray-700 dark:text-gray-300">
                    只是那个<strong class="font-black text-gray-900 dark:text-white">点火</strong>，并<strong class="font-black text-gray-900 dark:text-white">守护火焰</strong>的人。
                </p>
            </div>
        </section>

    </main>
    
    <footer class="fade-in-up delay-4 sticky bottom-4 w-full flex justify-center z-50">
        <div class="mx-auto flex items-center gap-4 rounded-full px-4 py-2 bg-white/50 dark:bg-black/30 backdrop-blur-lg shadow-lg border border-gray-200/50 dark:border-white/10">
            <span class="text-xs text-gray-600 dark:text-gray-400">A FATHER'S MANIFESTO</span>
            <div class="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <button id="theme-toggle" class="w-10 h-10 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-black/5 dark:hover:bg-white/10 transition-colors">
                <i class="fa-solid fa-sun block dark:hidden"></i>
                <i class="fa-solid fa-moon hidden dark:block"></i>
            </button>
        </div>
    </footer>


    <script>
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                }
            });
        }, {
            threshold: 0.1
        });

        document.querySelectorAll('.fade-in-up').forEach(el => {
            observer.observe(el);
        });

        const themeToggle = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;

        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }

        themeToggle.addEventListener('click', () => {
            htmlElement.classList.toggle('dark');
            if (htmlElement.classList.contains('dark')) {
                localStorage.theme = 'dark';
            } else {
                localStorage.theme = 'light';
            }
        });
    </script>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>