<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体工作流：模式、用例、示例等全方位解析</title>
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="../../css/styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #ffffff; /* 白色背景 */
            color: #000000; /* 纯黑色文字 */
            overflow-x: hidden;
        }
        .gradient-text {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
        .highlight-blue-purple {
            background-image: linear-gradient(to right, #3b82f6, #8b5cf6); /* 蓝到紫 */
        }
        .highlight-purple-red {
            background-image: linear-gradient(to right, #8b5cf6, #ef4444); /* 紫到红 */
        }
        .highlight-blue-green {
            background-image: linear-gradient(to right, #2563eb, #10b981); /* 蓝到绿 */
        }
        .highlight-orange-yellow {
            background-image: linear-gradient(to right, #f97316, #facc15); /* 橘红到黄 */
        }
        .section-container {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        @media (min-width: 1920px) {
            .section-container {
                max-width: 1600px;
            }
        }
        .超大字体 {
            font-size: clamp(2.8rem, 7vw, 5.5rem); /* 调整以适应更长的标题 */
            font-weight: 800;
            line-height: 1.1;
        }
        .中文大字体 {
            font-size: clamp(1.8rem, 4.5vw, 3rem);
            font-weight: 700;
        }
        .英文小字 {
            font-size: clamp(0.75rem, 1.4vw, 0.9rem);
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            opacity: 0.8;
        }
        .card {
            background-color: rgba(0,0,0,0.03);
            border-radius: 1rem;
            padding: 2rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%; /* 确保卡片等高 */
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        }
        .pattern-card { /* 特定用于模式部分的卡片 */
            background-color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .icon-bg-gradient {
            padding: 0.5rem;
            border-radius: 9999px; /* 圆形 */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }
        .workflow-diagram-box {
            border: 2px solid rgba(0,0,0,0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
            font-size: 0.9rem;
            margin: 0.5rem;
            background-color: rgba(255,255,255,0.5);
        }
        .workflow-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-size: 1.5rem;
            color: rgba(0,0,0,0.4);
        }
    </style>
</head>
<body class="antialiased">

    <!-- Hero Section -->
    <div class="section-container min-h-screen flex flex-col justify-center items-center text-center relative overflow-hidden">
        <div class="absolute inset-0 opacity-20">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="techGrid" width="80" height="80" patternUnits="userSpaceOnUse">
                        <path d="M 80 0 L 0 0 0 80" fill="none" stroke="rgba(59,130,246,0.3)" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#techGrid)" />
            </svg>
        </div>
        <div class="relative z-10">
            <p class="英文小字 mb-4">PATTERNS, USE CASES, EXAMPLES, AND MORE</p>
            <h1 class="超大字体 mb-4">
                <span class="gradient-text highlight-blue-purple">智能体工作流</span>究竟是什么？
            </h1>
            <p class="text-lg md:text-xl max-w-3xl mx-auto leading-relaxed mb-6">
                AI智能体、智能体架构、智能体工作流... 术语繁多。本文旨在拨开迷雾、阐释关键概念，助您理解AI智能体如何运作及智能体工作流的真正含义。
                <span class="英文小字 block mt-2">CUTTING THROUGH THE NOISE AND HYPE OF AGENTIC AI.</span>
            </p>
            <div class="text-xs text-gray-600">
                <p>作者: Mary Newhauser, Prajwal Yadav, Leonie Monigatti, Tuana Çelik (模拟)</p>
                <p class="mt-1">信息来源: Weaviate Blog</p>
            </div>
        </div>
        <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2">
            <i class="fas fa-chevron-down text-3xl opacity-50 animate-bounce"></i>
        </div>
    </div>

    <!-- Section: 什么是 AI 智能体? (What are AI agents?) -->
    <div class="section-container">
        <div class="text-center mb-12">
            <h2 class="中文大字体 gradient-text highlight-blue-green">什么是 AI 智能体？</h2>
            <p class="英文小字">DEFINING AI AGENTS</p>
        </div>
        <div class="grid md:grid-cols-2 gap-12 items-center">
            <div>
                <p class="text-lg md:text-xl leading-relaxed mb-6">
                    AI 智能体是结合了 <strong class="gradient-text highlight-orange-yellow">大语言模型 (LLMs)</strong> 进行推理和决策，并利用 <strong class="gradient-text highlight-purple-red">工具 (Tools)</strong> 与真实世界交互的系统。它们能够以有限的人工参与完成复杂任务。
                </p>
                <p class="text-lg md:text-xl leading-relaxed mb-4">
                    智能体被赋予特定角色和不同程度的自主权以实现其最终目标。它们还配备了 <strong class="gradient-text highlight-blue-purple">记忆 (Memory)</strong>，使其能够从过去的经验中学习并随时间提升性能。
                    <span class="英文小字 block mt-2">SYSTEMS COMBINING LLMS FOR REASONING WITH TOOLS FOR REAL-WORLD INTERACTION.</span>
                </p>
            </div>
            <div class="flex justify-center items-center p-6 rounded-lg" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);">
                <div class="w-full max-w-md">
                    <h3 class="text-2xl font-bold text-center mb-4">AI 智能体核心组件</h3>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-md shadow">
                            <i class="fas fa-brain text-2xl gradient-text highlight-blue-purple mr-3"></i>
                            <span><strong class="font-semibold">LLM (大语言模型):</strong> 提供推理能力 (规划、反思)。</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-md shadow">
                            <i class="fas fa-tools text-2xl gradient-text highlight-blue-green mr-3"></i>
                            <span><strong class="font-semibold">工具 (Tools):</strong> 与外部服务交互 (如API, 搜索)。</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-md shadow">
                            <i class="fas fa-memory text-2xl gradient-text highlight-orange-yellow mr-3"></i>
                            <span><strong class="font-semibold">记忆 (Memory):</strong> 存储信息 (短期、长期)。</span>
                        </div>
                         <div class="flex items-center p-3 bg-white rounded-md shadow">
                            <i class="fas fa-cogs text-2xl gradient-text highlight-purple-red mr-3"></i>
                            <span><strong class="font-semibold">提示/指令 (Prompt/Instructions):</strong> 定义任务和角色。</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section: 什么是智能体工作流? (What are Agentic Workflows?) -->
    <div class="section-container bg-gray-50">
        <div class="text-center mb-12">
            <h2 class="中文大字体 gradient-text highlight-purple-red">什么是智能体工作流？</h2>
            <p class="英文小字">UNDERSTANDING AGENTIC WORKFLOWS</p>
        </div>
        <p class="text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8 text-center">
            通常，一个 <strong class="gradient-text highlight-blue-purple">工作流 (Workflow)</strong> 是一系列为达成特定任务或目标而设计的关联步骤。
            而一个 <strong class="gradient-text highlight-blue-green">智能体工作流 (Agentic Workflow)</strong> 则是由一个或多个AI智能体 <strong class="gradient-text highlight-orange-yellow">动态执行</strong> 的一系列关联步骤。智能体被用户授予权限，使其拥有一定程度的自主权来收集数据、执行任务并在真实世界中做出决策。
        </p>
        <div class="mt-10">
            <h3 class="text-2xl font-bold text-center mb-6">三种工作流对比</h3>
            <div class="grid lg:grid-cols-3 gap-6 items-start">
                <div class="card p-6">
                    <h4 class="text-xl font-semibold mb-3 text-center">传统自动化工作流</h4>
                    <p class="text-xs text-center mb-3 text-gray-600">(基于规则, 非AI)</p>
                    <div class="flex flex-col items-center space-y-2">
                        <div class="workflow-diagram-box w-full">用户查询</div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">预定义步骤 1...N</div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">响应</div>
                    </div>
                </div>
                <div class="card p-6">
                    <h4 class="text-xl font-semibold mb-3 text-center">AI 工作流 <span class="text-gray-500">(非智能体)</span></h4>
                    <p class="text-xs text-center mb-3 text-gray-600">(使用AI模型, 但非自主)</p>
                     <div class="flex flex-col items-center space-y-2">
                        <div class="workflow-diagram-box w-full">用户查询</div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full flex items-center justify-center">
                            <i class="fas fa-robot mr-2 gradient-text highlight-blue-purple"></i> AI模型处理
                        </div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">响应</div>
                    </div>
                </div>
                <div class="card p-6 border-2 border-[#3b82f6]">
                    <h4 class="text-xl font-semibold mb-3 text-center gradient-text highlight-blue-purple">智能体工作流</h4>
                    <p class="text-xs text-center mb-3 text-gray-600">(AI智能体驱动, 动态自主)</p>
                    <div class="flex flex-col items-center space-y-1 text-sm">
                        <div class="workflow-diagram-box w-full">用户查询</div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">
                            <i class="fas fa-lightbulb mr-1 gradient-text highlight-orange-yellow"></i>制定计划 (Planning)
                        </div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">
                            <i class="fas fa-hammer mr-1 gradient-text highlight-blue-green"></i>执行动作 (Tools)
                        </div>
                         <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">
                            <i class="fas fa-sync-alt mr-1 gradient-text highlight-purple-red"></i>反思结果 (Reflection)
                            <span class="text-xs block text-gray-500">(如果结果不OK，则循环)</span>
                        </div>
                        <div class="workflow-arrow transform rotate-90 lg:rotate-0"><i class="fas fa-long-arrow-alt-down lg:fas fa-long-arrow-alt-right"></i></div>
                        <div class="workflow-diagram-box w-full">响应 (结果OK)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section: 是什么让工作流具有“智能体性”? (What makes a workflow agentic?) -->
    <div class="section-container">
        <div class="text-center mb-12">
            <h2 class="中文大字体 gradient-text highlight-blue-green">何为“智能体性”？</h2>
            <p class="英文小字">WHAT MAKES A WORKFLOW AGENTIC?</p>
        </div>
        <p class="text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-10 text-center">
            当一个或多个AI智能体引导和塑造任务进展时，AI工作流就具备了“智能体性”。其关键在于智能体的能力：
        </p>
        <div class="grid md:grid-cols-3 gap-8">
            <div class="card text-center">
                <div class="icon-bg-gradient highlight-blue-purple mb-4 w-16 h-16 mx-auto">
                    <i class="fas fa-lightbulb text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold mb-2">制定计划 <span class="英文小字 block">MAKE A PLAN</span></h3>
                <p class="text-sm leading-relaxed flex-grow">通过任务分解，将复杂任务拆分为更小的子任务，并确定最佳执行路径。</p>
            </div>
            <div class="card text-center">
                <div class="icon-bg-gradient highlight-blue-green mb-4 w-16 h-16 mx-auto">
                    <i class="fas fa-tools text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold mb-2">执行动作 <span class="英文小字 block">EXECUTE ACTIONS</span></h3>
                <p class="text-sm leading-relaxed flex-grow">使用预定义的工具集和权限来完成任务并执行其生成的计划。</p>
            </div>
            <div class="card text-center">
                <div class="icon-bg-gradient highlight-purple-red mb-4 w-16 h-16 mx-auto">
                    <i class="fas fa-sync-alt text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold mb-2">反思迭代 <span class="英文小字 block">REFLECT & ITERATE</span></h3>
                <p class="text-sm leading-relaxed flex-grow">评估每一步的结果，必要时调整计划，并循环直到结果满意为止。</p>
            </div>
        </div>
    </div>

    <!-- Section: 智能体工作流中的模式 (Patterns in Agentic Workflows) -->
    <div class="section-container bg-gray-50">
        <div class="text-center mb-12">
            <h2 class="中文大字体 gradient-text highlight-orange-yellow">智能体工作流核心模式</h2>
            <p class="英文小字">CORE PATTERNS IN AGENTIC WORKFLOWS</p>
        </div>
        <p class="text-lg md:text-xl leading-relaxed max-w-4xl mx-auto mb-10 text-center">
            智能体工作流是为完成特定最终目标而采取的结构化步骤系列。智能体的行为模式在其能力中扮演关键角色，核心模式包括<strong class="gradient-text highlight-blue-purple">规划</strong>、<strong class="gradient-text highlight-blue-green">工具使用</strong>、<strong class="gradient-text highlight-purple-red">反思</strong>以及<strong class="gradient-text highlight-orange-yellow">多智能体协作</strong>。
        </p>
        <div class="grid md:grid-cols-2 gap-10">
            <div class="pattern-card p-6 rounded-lg">
                <h3 class="text-3xl font-bold mb-3 gradient-text highlight-blue-purple">规划模式 <span class="英文小字 block">PLANNING PATTERN</span></h3>
                <div class="flex justify-center my-4">
                    <i class="fas fa-sitemap text-6xl opacity-30 gradient-text highlight-blue-purple"></i>
                </div>
                <p class="text-md leading-relaxed">
                    允许智能体自主地将更复杂的任务分解为一系列更小、更简单的任务（任务分解）。这能减轻LLM的认知负荷，改进推理，并最大限度地减少幻觉和其他不准确性。
                    <span class="英文小字 block mt-2">AUTONOMOUS TASK DECOMPOSITION FOR COMPLEX PROBLEMS.</span>
                </p>
            </div>
            <div class="pattern-card p-6 rounded-lg">
                <h3 class="text-3xl font-bold mb-3 gradient-text highlight-blue-green">工具使用模式 <span class="英文小字 block">TOOL USE PATTERN</span></h3>
                 <div class="flex justify-center my-4">
                    <i class="fas fa-plug text-6xl opacity-30 gradient-text highlight-blue-green"></i>
                </div>
                <p class="text-md leading-relaxed">
                    扩展了智能体的能力，使其能够与外部资源和应用程序（如API、数据库、代码解释器）动态交互，而不仅仅是检索数据。检索增强生成 (RAG) 是该模式的一个例子。
                    <span class="英文小字 block mt-2">DYNAMIC INTERACTION WITH EXTERNAL RESOURCES AND APPLICATIONS.</span>
                </p>
            </div>
            <div class="pattern-card p-6 rounded-lg">
                <h3 class="text-3xl font-bold mb-3 gradient-text highlight-purple-red">反思模式 <span class="英文小字 block">REFLECTION PATTERN</span></h3>
                <div class="flex justify-center my-4">
                    <i class="fas fa-search-location text-6xl opacity-30 gradient-text highlight-purple-red"></i>
                </div>
                <p class="text-md leading-relaxed">
                    智能体评估自身行动的结果、识别错误、并从经验中学习以优化未来步骤。这使得工作流能够自我纠正、适应不断变化的环境，并持续改进其性能。
                    <span class="英文小字 block mt-2">SELF-CORRECTION, ADAPTATION, AND CONTINUOUS IMPROVEMENT.</span>
                </p>
            </div>
            <div class="pattern-card p-6 rounded-lg">
                <h3 class="text-3xl font-bold mb-3 gradient-text highlight-orange-yellow">多智能体协作模式 <span class="英文小字 block">MULTI-AGENT COLLABORATION</span></h3>
                 <div class="flex justify-center my-4">
                    <i class="fas fa-users-cog text-6xl opacity-30 gradient-text highlight-orange-yellow"></i>
                </div>
                <p class="text-md leading-relaxed">
                    涉及多个具有不同专长或角色的智能体协同工作。它们通过任务分配、信息共享和结果整合来共同解决单个智能体难以应对的复杂问题，实现1+1>2的效果。
                    <span class="英文小字 block mt-2">SYNERGISTIC PROBLEM-SOLVING WITH SPECIALIZED AGENTS.</span>
                </p>
            </div>
        </div>
        <p class="text-sm text-gray-600 mt-8 text-center">注意：上述为简化示意。实际模式可能更复杂，并包含更多步骤和反馈循环。</p>
    </div>

    <!-- Section: 优势 (Benefits) -->
    <div class="section-container">
        <div class="text-center mb-16">
            <h2 class="中文大字体 gradient-text highlight-blue-green">为何选择？巨大优势</h2>
            <p class="英文小字">WHY AGENTIC? THE ADVANTAGES</p>
        </div>
        <div class="grid md:grid-cols-2 gap-8">
            <div class="p-6 rounded-lg" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);">
                <h3 class="text-5xl font-extrabold gradient-text highlight-blue-purple mb-3">能力跃迁</h3>
                <p class="英文小字 mb-2">POWER BOOST</p>
                <p class="text-lg">解决单一智能体无法处理的极度复杂问题。</p>
            </div>
            <div class="p-6 rounded-lg" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(249, 115, 22, 0.1) 100%);">
                <h3 class="text-5xl font-extrabold gradient-text highlight-blue-green mb-3">自动化大师</h3>
                <p class="英文小字 mb-2">AUTOMATION MASTER</p>
                <p class="text-lg">自动执行多步骤、需多种技能配合的复杂流程。</p>
            </div>
            <div class="p-6 rounded-lg" style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);">
                <h3 class="text-5xl font-extrabold gradient-text highlight-purple-red mb-3">效率倍增</h3>
                <p class="英文小字 mb-2">EFFICIENCY xN</p>
                <p class="text-lg">通过并行处理和专业化分工大幅提升任务完成速度。</p>
            </div>
            <div class="p-6 rounded-lg" style="background: linear-gradient(135deg, rgba(217, 70, 239, 0.1) 0%, rgba(96, 165, 250, 0.1) 100%);">
                <h3 class="text-5xl font-extrabold gradient-text highlight-orange-yellow mb-3">持续进化</h3>
                <p class="英文小字 mb-2">ALWAYS LEARNING</p>
                <p class="text-lg">工作流能够根据反馈和新数据进行动态调整和自我改进。</p>
            </div>
        </div>
    </div>

    <!-- Section: 应用示例 (Use Cases) -->
     <div class="section-container bg-gray-50">
        <div class="text-center mb-16">
            <h2 class="中文大字体 gradient-text highlight-orange-yellow">实践应用</h2>
            <p class="英文小字">IN ACTION: USE CASES</p>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card p-8 flex flex-col items-center text-center">
                <i class="fas fa-search-plus text-6xl mb-6 gradient-text highlight-blue-purple" style="opacity:0.8;"></i>
                <h3 class="text-6xl md:text-8xl font-black gradient-text highlight-blue-purple opacity-50 leading-none">研究</h3>
                <p class="英文小字 mt-1">RESEARCH & ANALYSIS</p>
                <p class="mt-3 text-sm flex-grow">自动收集、处理和总结海量信息，洞察先机。</p>
            </div>
            <div class="card p-8 flex flex-col items-center text-center">
                <i class="fas fa-pencil-alt text-6xl mb-6 gradient-text highlight-blue-green" style="opacity:0.8;"></i>
                <h3 class="text-6xl md:text-8xl font-black gradient-text highlight-blue-green opacity-50 leading-none">创作</h3>
                <p class="英文小字 mt-1">CONTENT CREATION</p>
                <p class="mt-3 text-sm flex-grow">生成从博客文章到营销文案乃至代码等多样化内容。</p>
            </div>
            <div class="card p-8 flex flex-col items-center text-center">
                <i class="fas fa-code text-6xl mb-6 gradient-text highlight-purple-red" style="opacity:0.8;"></i>
                <h3 class="text-6xl md:text-8xl font-black gradient-text highlight-purple-red opacity-50 leading-none">开发</h3>
                <p class="英文小字 mt-1">SOFTWARE DEVELOPMENT</p>
                <p class="mt-3 text-sm flex-grow">辅助编码、测试、调试和文档编写，加速迭代。</p>
            </div>
        </div>
    </div>

    <!-- Section: 挑战 (Challenges) -->
    <div class="section-container">
        <div class="text-center mb-16">
            <h2 class="中文大字体 gradient-text highlight-purple-red">前路挑战</h2>
            <p class="英文小字">THE ROAD AHEAD: CHALLENGES</p>
        </div>
        <div class="max-w-2xl mx-auto space-y-8">
            <div class="flex items-start space-x-4 p-4 rounded-lg border border-purple-200 hover:shadow-lg transition-shadow">
                <i class="fas fa-project-diagram text-3xl gradient-text highlight-blue-purple mt-1 flex-shrink-0 w-8 text-center"></i>
                <div>
                    <h3 class="text-2xl font-bold">设计复杂度 <span class="英文小字">COMPLEXITY</span></h3>
                    <p class="text-md">构建和协调多个智能体，确保它们高效、无缝协作，是一项复杂的设计挑战。</p>
                </div>
            </div>
            <div class="flex items-start space-x-4 p-4 rounded-lg border border-green-200 hover:shadow-lg transition-shadow">
                <i class="fas fa-server text-3xl gradient-text highlight-blue-green mt-1 flex-shrink-0 w-8 text-center"></i>
                <div>
                    <h3 class="text-2xl font-bold">资源消耗 <span class="英文小字">RESOURCES</span></h3>
                    <p class="text-md">运行复杂的智能体工作流可能需要大量计算资源和能源，成本控制是关键。</p>
                </div>
            </div>
            <div class="flex items-start space-x-4 p-4 rounded-lg border border-orange-200 hover:shadow-lg transition-shadow">
                <i class="fas fa-shield-alt text-3xl gradient-text highlight-orange-yellow mt-1 flex-shrink-0 w-8 text-center"></i>
                <div>
                    <h3 class="text-2xl font-bold">伦理与安全 <span class="英文小字">ETHICS & SECURITY</span></h3>
                    <p class="text-md">必须关注和解决偏见、错误信息传播、数据隐私以及潜在滥用等重要问题。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section: 未来 (The Future) -->
    <div class="section-container bg-gray-900 text-white">
        <div class="text-center mb-12">
            <h2 class="中文大字体 gradient-text highlight-blue-purple">未来已来</h2>
            <p class="英文小字 text-gray-400">THE FUTURE IS AGENTIC</p>
        </div>
        <div class="text-center max-w-3xl mx-auto">
            <p class="text-xl md:text-2xl leading-relaxed mb-8">
                智能体工作流正为我们开启一个 <strong class="gradient-text highlight-blue-green">更自主、更强大、更具协作性</strong> 的 AI 新纪元。它们将日益成为我们跨行业解决问题和推动创新的核心力量，重塑未来工作与生活方式。
            </p>
            <div class="relative w-full max-w-md mx-auto h-64">
                <svg width="100%" height="100%" viewBox="0 0 300 150">
                    <defs>
                        <linearGradient id="futureGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="futureGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f97316;stop-opacity:1" />
                        </linearGradient>
                         <filter id="glow"><feGaussianBlur stdDeviation="2.5" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter>
                    </defs>
                    <circle cx="50" cy="75" r="10" fill="url(#futureGrad1)" class="node" filter="url(#glow)"/>
                    <circle cx="150" cy="40" r="15" fill="url(#futureGrad2)" class="node" filter="url(#glow)"/>
                    <circle cx="150" cy="110" r="12" fill="url(#futureGrad1)" class="node" filter="url(#glow)"/>
                    <circle cx="250" cy="75" r="18" fill="url(#futureGrad2)" class="node" filter="url(#glow)"/>
                    <line x1="50" y1="75" x2="150" y2="40" stroke="rgba(255,255,255,0.3)" stroke-width="2" class="link"/><line x1="50" y1="75" x2="150" y2="110" stroke="rgba(255,255,255,0.3)" stroke-width="2" class="link"/><line x1="150" y1="40" x2="250" y2="75" stroke="rgba(255,255,255,0.3)" stroke-width="2" class="link"/><line x1="150" y1="110" x2="250" y2="75" stroke="rgba(255,255,255,0.3)" stroke-width="2" class="link"/>
                    <style>.node { animation: pulse 2s infinite alternate; } .link { animation: dash 3s linear infinite; stroke-dasharray: 10,5; } @keyframes pulse { 0% { opacity: 0.6; transform: scale(0.95); } 100% { opacity: 1; transform: scale(1.05); } } @keyframes dash { to { stroke-dashoffset: -15; } }</style>
                </svg>
            </div>
             <a href="https://weaviate.io/blog/what-are-agentic-workflows" target="_blank" rel="noopener noreferrer"
               class="mt-12 inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg transition-transform duration-300 ease-in-out hover:scale-105"
               style="background-image: linear-gradient(to right, #3b82f6, #8b5cf6);">
                阅读原文 <span class="英文小字">READ ORIGINAL ARTICLE</span> <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center p-8 bg-black text-gray-400">
        <p class="text-sm">
            动态网页内容基于 Weaviate 博客文章 <a href="https://weaviate.io/blog/what-are-agentic-workflows" target="_blank" rel="noopener noreferrer" class="hover:text-white underline">"What Are Agentic Workflows? Patterns, Use Cases, Examples, and More"</a> 生成。
            <span class="block mt-1">DYNAMIC CONTENT GENERATED BASED ON WEAVIATE'S BLOG POST.</span>
        </p>
        <p class="text-xs mt-2">© 2024 动态网页生成演示。所有信息版权归原作者所有。</p>
    </footer>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>