<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-T">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude 4 系统提示词要点解析 - <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.4.3/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../css/styles.css">
    <style>
        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa; /* 极浅灰色背景 */
        }
        .content-section {
            background-color: white;
            padding: 2rem; /* 2rem = 32px */
            border-radius: 0.75rem; /* 12px */
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            margin-bottom: 3rem; /* 48px */
        }
        .content-section h2 {
            font-size: 2.25rem; /* 36px */
            font-weight: 700; /* bold */
            margin-bottom: 1.5rem; /* 24px */
            display: flex;
            align-items: center;
        }
        .content-section h2 .material-icons {
            font-size: 2.5rem; /* 40px */
            margin-right: 0.75rem; /* 12px */
            opacity: 0.9;
        }
        .content-section p, .content-section ul {
            font-size: 1.125rem; /* 18px */
            line-height: 1.8;
            color: #374151; /* cool-gray-700 */
            margin-bottom: 1rem;
        }
        .content-section ul {
            list-style-position: inside;
            padding-left: 0.5rem;
        }
        .content-section ul li::marker {
            color: #4F46E5; /* Indigo-600 - Google Blue-ish */
        }
        .content-section strong {
            font-weight: 700;
            color: #1f2937; /* cool-gray-800 */
        }
        .english-term {
            font-family: 'Roboto', sans-serif;
            font-size: 0.9em;
            color: #4B5563; /* cool-gray-600 */
            font-weight: 500;
            margin-left: 0.25rem;
        }
        .highlight-block {
            padding: 1.5rem; /* 24px */
            border-radius: 0.5rem; /* 8px */
            margin-top: 1rem;
            margin-bottom: 1.5rem;
        }
        .google-blue-bg { background-color: rgba(66, 133, 244, 0.1); border-left: 4px solid #4285F4; } /* Google Blue */
        .google-yellow-bg { background-color: rgba(251, 188, 5, 0.1); border-left: 4px solid #FBBC05; } /* Google Yellow */
        .google-green-bg { background-color: rgba(52, 168, 83, 0.1); border-left: 4px solid #34A853; } /* Google Green */
        .google-red-bg { background-color: rgba(234, 67, 53, 0.1); border-left: 4px solid #EA4335; } /* Google Red */

        .main-title {
            font-size: 4.5rem; /* 72px */
            font-weight: 900; /* black */
            color: #4285F4; /* Google Blue */
            line-height: 1.1;
        }
        .sub-title {
            font-size: 2rem; /* 32px */
            color: #5f6368; /* Google Gray */
            margin-top: 0.5rem;
            font-weight: 500;
        }
        .source-link {
            color: #4285F4; /* Google Blue */
            font-weight: 700;
            transition: color 0.3s ease, transform 0.3s ease;
            display: inline-flex;
            align-items: center;
        }
        .source-link:hover {
            color: #1a73e8; /* Darker Google Blue */
            transform: translateX(2px);
        }
        .source-link .material-icons {
            transition: transform 0.3s ease;
        }
        .source-link:hover .material-icons {
            transform: translateX(4px);
        }
        .font-noto-sans-sc { font-family: 'Noto Sans SC', sans-serif; }
        .font-roboto { font-family: 'Roboto', sans-serif; }

        /* Responsive font sizes */
        @media (max-width: 768px) {
            .main-title { font-size: 3rem; /* 48px */ }
            .sub-title { font-size: 1.5rem; /* 24px */ }
            .content-section h2 { font-size: 1.75rem; /* 28px */ }
            .content-section h2 .material-icons { font-size: 2rem; /* 32px */ }
            .content-section p, .content-section ul { font-size: 1rem; /* 16px */ }
        }
        .chinese-bold-large {
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 700; /* bold */
        }
        .english-small-embellish {
            font-family: 'Roboto', sans-serif;
            font-size: 0.8em;
            color: #5f6368; /* Google Gray */
            margin-left: 0.25rem;
            font-weight: 400;
        }
    </style>
</head>
<body class="text-gray-800">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-20 max-w-screen-xl">
        <header class="text-center mb-16 md:mb-24">
            <h1 class="main-title font-roboto">
                <span class="font-noto-sans-sc">Claude 4</span>
                <span class="block md:inline english-small-embellish opacity-70" style="font-size: 0.5em; vertical-align: super;">System Prompt</span>
            </h1>
            <p class="sub-title font-noto-sans-sc">系统提示词核心要点解析</p>
            <p class="mt-4 text-lg text-gray-500 font-roboto">
                Insights from <span class="font-semibold">Simon Willison's</span> analysis
            </p>
        </header>

        <main>
            <section class="content-section">
                <h2 class="text-blue-600"><span class="material-icons text-blue-500">article</span><span class="chinese-bold-large">引言</span><span class="english-small-embellish">Introduction</span></h2>
                <p>本文以 <strong class="chinese-bold-large">Anthropic</strong> 公开的 <strong class="chinese-bold-large">Claude Opus 4</strong> 和 <strong class="chinese-bold-large">Claude Sonnet 4</strong> 的系统提示词为切入点，指出这些提示词不仅是模型行为的“说明书”，更反映了模型开发者对 <span class="font-roboto">AI</span> 助手角色的深刻思考。作者还对比了官方发布与泄露的工具提示词，揭示了 <strong class="chinese-bold-large">Claude 4</strong> 背后更为复杂和细致的设计细节。</p>
            </section>

            <section class="content-section">
                <h2 class="text-yellow-600"><span class="material-icons text-yellow-500">badge</span><span class="chinese-bold-large">模型设定与用户引导</span><span class="english-small-embellish">Identity & Guidance</span></h2>
                <p>系统提示词开头部分为模型设定了身份、当前日期，并提供了关于 <strong class="chinese-bold-large">Claude</strong> 及其产品的信息，方便模型在用户提问时自我介绍。提示词还明确要求模型在面对产品相关问题（如消息数量、费用、操作方法等）时，需引导用户前往官方支持页面，而不是直接回答。</p>
            </section>

            <section class="content-section">
                <h2 class="text-green-600"><span class="material-icons text-green-500">psychology</span><span class="chinese-bold-large">个性设定</span><span class="english-small-embellish">Personality Traits</span></h2>
                <p>在“个性设定”部分，<strong class="chinese-bold-large">Claude</strong> 被要求在面对用户不满或无礼时，保持正常回应并引导用户反馈；在被问及偏好或经历时，以假设性方式作答但不主动说明是假设。<strong class="chinese-bold-large">Anthropic</strong> 强调，模型应坦诚自身存在偏见和局限，避免营造“绝对客观”的假象。</p>
            </section>

            <section class="content-section">
                <h2 class="text-red-600"><span class="material-icons text-red-500">health_and_safety</span><span class="chinese-bold-large">情感支持与安全</span><span class="english-small-embellish">Emotional Support & Safety</span></h2>
                <p><strong class="chinese-bold-large">Claude</strong> 需在提供医学或心理信息时兼顾情感关怀，坚决避免鼓励自毁行为，并对涉及未成年人的内容格外谨慎。模型被严格禁止生成与武器、恶意代码、网络攻击等相关的信息，即使用户声称出于正当目的也不例外。同时，<strong class="chinese-bold-large">Claude</strong> 在面对模糊请求时，倾向于假定用户意图合法。</p>
            </section>

            <section class="content-section">
                <h2 class="text-indigo-600"><span class="material-icons text-indigo-500">draw</span><span class="chinese-bold-large">风格和表达</span><span class="english-small-embellish">Style & Expression</span></h2>
                <p><strong class="chinese-bold-large">Claude</strong> 在日常对话中应保持自然、温暖、共情的语气，避免使用列表和过多的 <span class="font-roboto">markdown</span> 格式，除非用户明确要求。对于简单问题要简明作答，复杂问题则需详尽阐述，并善用例子、比喻等方式解释难点。模型还被要求在面对自身意识、情感等哲学性问题时，采取开放但不确定的态度。</p>
            </section>

            <div class="grid md:grid-cols-2 gap-x-8 gap-y-12">
                <section class="content-section">
                    <h2 class="text-teal-600"><span class="material-icons text-teal-500">event_busy</span><span class="chinese-bold-large">知识截止日期</span><span class="english-small-embellish">Knowledge Cutoff</span></h2>
                    <p>虽然官方资料称 <strong class="chinese-bold-large">Claude 4</strong> 的训练数据截止到 <strong class="chinese-bold-large">2025年3月</strong>，但系统提示词要求模型以 <strong class="chinese-bold-large">2025年1月底</strong> 为知识界限，对此之后的事件不做肯定或否定回应。</p>
                </section>

                <section class="content-section">
                    <h2 class="text-purple-600"><span class="material-icons text-purple-500">gavel</span><span class="chinese-bold-large">特定信息处理</span><span class="english-small-embellish">Specific Information Handling</span></h2>
                    <p>在美国大选相关信息上，系统提示词明确写明 <strong class="chinese-bold-large">2024年</strong> 大选由特朗普胜出，但要求模型仅在相关提问时才提及此信息，避免主动输出。</p>
                </section>
            </div>

            <section class="content-section">
                 <h2 class="text-orange-600"><span class="material-icons text-orange-500">thumb_down_off_alt</span><span class="chinese-bold-large">沟通准则</span><span class="english-small-embellish">Communication Rule</span></h2>
                <p><strong class="chinese-bold-large">Claude</strong> 还被反复提醒不要“拍马屁”，即不以“好问题”“很棒的想法”等溢美之词开头，直接进入正题。</p>
            </section>

            <section class="content-section">
                <h2 class="text-cyan-600"><span class="material-icons text-cyan-500">compare_arrows</span><span class="chinese-bold-large">模型差异与迭代</span><span class="english-small-embellish">Model Differences & Iterations</span></h2>
                <p>文章还分析了 <strong class="chinese-bold-large">Opus 4</strong> 与 <strong class="chinese-bold-large">Sonnet 4</strong> 提示词的细微差别，以及 <strong class="chinese-bold-large">Claude 3.7</strong> 时代被移除的部分（如计数和谜题处理的特殊指令），并指出新模型在某些经典问题上表现有所提升，但仍有改进空间。</p>
            </section>

            <section class="content-section google-blue-bg">
                <h2 class="text-blue-700" style="font-size: 2.5rem; /* 40px */"><span class="material-icons text-blue-600" style="font-size: 3rem;">build_circle</span><span class="chinese-bold-large">核心揭秘：工具提示词</span><span class="english-small-embellish">Leaked Tool Prompts</span></h2>
                <p class="text-lg font-medium text-blue-800">最引人关注的是泄露的工具提示词部分。这里详细描述了 <strong class="chinese-bold-large">Claude 4</strong> 的“思考模式” (<span class="font-roboto">interleaved thinking</span>)、工具调用流程、搜索策略及版权合规要求。</p>
                <ul class="list-disc ml-4 space-y-2">
                    <li><strong class="chinese-bold-large">搜索策略</strong>：<strong class="chinese-bold-large">Claude</strong> 在使用搜索工具时，需优先用自身知识，仅在必要时调用搜索，并严格限制引用内容长度，绝不输出超过 <strong class="chinese-bold-large">15词</strong> 的原文或任何歌词，且不得生成“置换性摘要”。</li>
                    <li><strong class="chinese-bold-large">复杂研究</strong>：对于复杂的“研究类”问题，<strong class="chinese-bold-large">Claude</strong> 会自动调用多种工具，综合多源信息，确保答案全面且有深度。</li>
                </ul>
            </section>

            <section class="content-section google-green-bg">
                <h2 class="text-green-700" style="font-size: 2.5rem;"><span class="material-icons text-green-600" style="font-size: 3rem;">widgets</span><span class="chinese-bold-large">创新功能：Artifacts 设计原则</span><span class="english-small-embellish">Artifacts Design Principles</span></h2>
                <p class="text-lg font-medium text-green-800">在 <span class="font-roboto">“Artifacts”</span> 功能部分，系统提示词为 <strong class="chinese-bold-large">Claude</strong> 生成可交互网页、可视化组件等提供了详细设计原则。</p>
                <ul class="list-disc ml-4 space-y-2">
                    <li><strong class="chinese-bold-large">核心原则</strong>：包括优先保证功能性、现代美学、动效与可访问性。</li>
                    <li><strong class="chinese-bold-large">技术栈</strong>：列举了支持的第三方库和技术限制（如禁止使用 <span class="font-roboto">localStorage</span> 等浏览器存储 <span class="font-roboto">API</span>）。</li>
                </ul>
                 <div class="mt-6 p-4 bg-white/70 rounded-md shadow">
                    <p class="text-sm text-gray-700"><strong class="chinese-bold-large">设计重点：</strong> <span class="font-roboto">Functionality first, modern aesthetics, subtle animations, and accessibility.</span></p>
                </div>
            </section>

            <section class="content-section">
                <h2 class="text-pink-600"><span class="material-icons text-pink-500">palette</span><span class="chinese-bold-large">自定义：风格功能</span><span class="english-small-embellish">Customizable Styles</span></h2>
                <p>系统提示词还涵盖了“风格”功能，允许用户自定义 <strong class="chinese-bold-large">Claude</strong> 的写作风格，并规定模型在风格与用户新指令冲突时应以最新指令为准。</p>
            </section>

            <section class="content-section google-yellow-bg">
                <h2 class="text-yellow-700"><span class="material-icons text-yellow-600">emoji_objects</span><span class="chinese-bold-large">总结与评价</span><span class="english-small-embellish">Conclusion & Evaluation</span></h2>
                <p class="text-lg font-medium text-yellow-800">文章认为 <strong class="chinese-bold-large">Anthropic</strong> 在 <strong class="chinese-bold-large">Claude 4</strong> 的系统提示词设计上展现了极高的透明度和细致入微的工程思维，不仅为用户和开发者提供了宝贵的参考，也为行业树立了良好范例。</p>
                <div class="mt-8 text-center">
                    <span class="material-icons text-yellow-500 text-7xl md:text-9xl" style="text-shadow: 2px 2px 8px rgba(0,0,0,0.1);">engineering</span>
                </div>
            </section>

        </main>

        <footer class="text-center mt-16 md:mt-24 py-8 border-t border-gray-200">
            <p class="text-gray-600 mb-2 font-noto-sans-sc">内容总结自 <strong class="chinese-bold-large">Simon Willison</strong> 的文章：</p>
            <a href="https://simonwillison.net/" target="_blank" rel="noopener noreferrer"
               class="text-xl source-link font-roboto">
                simonwillison.net
                <span class="material-icons text-2xl ml-1">arrow_forward</span>
            </a>
        </footer>
    </div>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>