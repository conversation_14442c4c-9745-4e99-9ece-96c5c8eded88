<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整的回归之路 - The Complete Path Home</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    
    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <style>
        /* 自定义全局样式 */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa; /* 极浅灰色背景 */
            color: #1d1d1f; /* 接近苹果的深灰色文本 */
            overflow-x: hidden;
        }

        /* 核心：色彩云雾/极光渐变背景 */
        .aurora-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -10;
            overflow: hidden;
        }

        .aurora-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px); /* 关键的模糊效果 */
            opacity: 0.25;
            animation: move 30s infinite alternate;
        }
        
        .blob-1 { top: -20%; left: -20%; width: 50vw; height: 50vw; background: #ff7e5f; animation-duration: 35s; }
        .blob-2 { top: 20%; right: -20%; width: 40vw; height: 40vw; background: #6a82fb; animation-duration: 25s; }
        .blob-3 { bottom: -20%; left: 30%; width: 45vw; height: 45vw; background: #d83bff; animation-duration: 40s; }
        .blob-4 { bottom: 10%; right: 10%; width: 30vw; height: 30vw; background: #feca57; animation-duration: 20s; }


        @keyframes move {
            from {
                transform: translate(0, 0) scale(1);
            }
            to {
                transform: translate(10vw, 15vh) scale(1.2);
            }
        }
        
        /* 滚动触发动画的基础样式 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 高亮色辉光效果 */
        .tech-glow-blue::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 150%;
            height: 100px;
            background: radial-gradient(circle, rgba(106, 130, 251, 0.3) 0%, rgba(106, 130, 251, 0) 70%);
            z-index: -1;
        }
        
        .tech-glow-purple::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 150%;
            height: 100px;
            background: radial-gradient(circle, rgba(216, 59, 255, 0.3) 0%, rgba(216, 59, 255, 0) 70%);
            z-index: -1;
        }

    </style>
</head>
<body class="antialiased">

    <!-- 动态极光背景 -->
    <div class="aurora-background">
        <div class="aurora-blob blob-1"></div>
        <div class="aurora-blob blob-2"></div>
        <div class="aurora-blob blob-3"></div>
        <div class="aurora-blob blob-4"></div>
    </div>

    <main class="relative z-10">

        <!-- Section 1: 战场 -->
        <section class="min-h-screen w-full flex flex-col items-center justify-center p-8 text-center overflow-hidden">
            <div class="scroll-reveal">
                <p class="text-xl md:text-2xl text-gray-500 mb-4 tracking-widest uppercase">The Battlefield</p>
                <h1 class="text-7xl md:text-8xl lg:text-9xl font-black tracking-tighter text-gray-800 leading-none">战场</h1>
                <p class="mt-8 text-2xl md:text-3xl lg:text-4xl max-w-4xl mx-auto text-gray-700">
                    面对疾病，你的身体是战场。
                </p>
            </div>
        </section>

        <!-- Section 2: 两股力量 -->
        <section class="min-h-screen w-full flex items-center justify-center p-8 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 lg:gap-24 w-full max-w-7xl mx-auto">
                <!-- 左侧：医学 -->
                <div class="scroll-reveal text-center md:text-left bg-white/30 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-xl">
                    <p class="text-base text-blue-600 mb-2 tracking-widest uppercase">External Army</p>
                    <h2 class="text-5xl md:text-6xl font-bold tracking-tight mb-4">医学</h2>
                    <p class="text-xl md:text-2xl text-gray-600 mb-8">
                        是你最精锐的<span class="font-bold text-blue-600">“外部军队”</span>。
                    </p>
                    <div class="text-6xl text-blue-500 relative inline-block tech-glow-blue">
                        <i class="fa-solid fa-shield-virus"></i>
                    </div>
                </div>
                <!-- 右侧：意念 -->
                <div class="scroll-reveal text-center md:text-right bg-white/30 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-xl" style="transition-delay: 200ms;">
                    <p class="text-base text-purple-600 mb-2 tracking-widest uppercase">Internal Resistance</p>
                    <h2 class="text-5xl md:text-6xl font-bold tracking-tight mb-4">意念</h2>
                    <p class="text-xl md:text-2xl text-gray-600 mb-8">
                        是你最坚韧的<span class="font-bold text-purple-600">“内在抵抗”</span>。
                    </p>
                    <div class="text-6xl text-purple-500 relative inline-block tech-glow-purple">
                         <i class="fa-solid fa-brain"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: 依存关系 -->
        <section class="min-h-screen w-full flex flex-col items-center justify-center p-8 text-center overflow-hidden">
             <div class="scroll-reveal mb-12">
                <p class="text-xl md:text-2xl text-gray-500 mb-4 tracking-widest uppercase">The Synergy</p>
                <h2 class="text-6xl md:text-7xl lg:text-8xl font-black tracking-tighter text-gray-800 leading-none">缺一不可</h2>
             </div>
             <div class="flex flex-col md:flex-row gap-8 max-w-5xl w-full">
                <div class="scroll-reveal w-full p-8 bg-white/20 backdrop-blur-lg rounded-2xl text-center border border-white/20">
                    <p class="text-2xl md:text-3xl text-gray-700">只靠抵抗，<br><span class="opacity-50">无法赢得战争。</span></p>
                </div>
                <div class="scroll-reveal w-full p-8 bg-white/20 backdrop-blur-lg rounded-2xl text-center border border-white/20" style="transition-delay: 200ms;">
                    <p class="text-2xl md:text-3xl text-gray-700">没有抵抗，<br><span class="opacity-50">军队将孤立无援。</span></p>
                </div>
             </div>
        </section>

        <!-- Section 4: 分工与疗愈 -->
        <section class="min-h-screen w-full flex flex-col items-center justify-center p-8 space-y-24 overflow-hidden">
            <div class="scroll-reveal text-center max-w-4xl">
                <p class="text-xl md:text-2xl text-gray-500 mb-4 tracking-widest uppercase">The Mission</p>
                <p class="text-2xl md:text-3xl lg:text-4xl text-gray-700">
                    所以，请让你的医生，去治你的<span class="text-blue-600 font-bold">“病”</span>。
                </p>
                <h2 class="text-8xl md:text-9xl lg:text-[12rem] font-black tracking-tighter text-blue-500/80 leading-none my-4">病</h2>
                <!-- 勾线图形化 - 靶向治疗 -->
                 <svg class="w-24 h-24 mx-auto text-blue-400" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="10" stroke="currentColor" stroke-width="2"/>
                    <path d="M50 0V25" stroke="currentColor" stroke-width="2"/>
                    <path d="M50 100V75" stroke="currentColor" stroke-width="2"/>
                    <path d="M0 50H25" stroke="currentColor" stroke-width="2"/>
                    <path d="M100 50H75" stroke="currentColor" stroke-width="2"/>
                    <path d="M25 25L40 40" stroke="currentColor" stroke-width="2"/>
                    <path d="M75 75L60 60" stroke="currentColor" stroke-width="2"/>
                    <path d="M25 75L40 60" stroke="currentColor" stroke-width="2"/>
                    <path d="M75 25L60 40" stroke="currentColor" stroke-width="2"/>
                </svg>
            </div>

            <div class="scroll-reveal text-center max-w-4xl" style="transition-delay: 300ms;">
                 <p class="text-2xl md:text-3xl lg:text-4xl text-gray-700">
                    而你，用你的意念，去疗愈你的<span class="text-purple-600 font-bold">“生命”</span>。
                </p>
                <h2 class="text-8xl md:text-9xl lg:text-[12rem] font-black tracking-tighter text-purple-500/80 leading-none my-4">生命</h2>
                <!-- 勾线图形化 - 生命活力 -->
                <svg class="w-24 h-24 mx-auto text-purple-400" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M50 15C30.67 15 15 30.67 15 50C15 69.33 30.67 85 50 85C69.33 85 85 69.33 85 50" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <path d="M50 15C69.33 15 85 30.67 85 50" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="4 8"/>
                    <path d="M50 35C41.7157 35 35 41.7157 35 50C35 58.2843 41.7157 65 50 65C58.2843 65 65 58.2843 65 50C65 41.7157 58.2843 35 50 35Z" stroke="currentColor" stroke-width="2"/>
                </svg>
            </div>
        </section>

        <!-- Section 5: 回归之路 -->
        <section class="min-h-screen w-full flex flex-col items-center justify-center p-8 text-center overflow-hidden">
            <div class="scroll-reveal">
                <p class="text-xl md:text-2xl text-gray-500 mb-8 tracking-widest uppercase">The Complete Path Home</p>
                <h2 class="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-8 max-w-4xl mx-auto">
                    这，才是完整的
                </h2>
                <h1 class="text-7xl md:text-8xl lg:text-9xl font-black tracking-tighter text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-purple-500 to-amber-400">
                    回归之路
                </h1>
                 <!-- 勾线图形化 - 完整的回归之路 -->
                 <svg class="w-48 h-16 mx-auto mt-16 text-gray-400" viewBox="0 0 200 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 25C35 5, 65 45, 100 25S 165 5, 195 25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="195" cy="25" r="4" fill="currentColor"/>
                </svg>
            </div>
        </section>

    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.2 // 元素可见20%时触发
            });

            const hiddenElements = document.querySelectorAll('.scroll-reveal');
            hiddenElements.forEach(el => observer.observe(el));
        });
    </script>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>