/* =============== UI组件样式 - 卡片、按钮、表单等 =============== */

/* =============== 标签页面样式 =============== */
/* 标签云样式优化 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    align-items: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(104, 144, 148, 0.1);
}

.tag-item {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: rgba(104, 144, 148, 0.1);
    color: var(--color-text-main);
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(104, 144, 148, 0.2);
    font-family: 'Noto Sans SC', sans-serif;
}

.tag-item:hover {
    background: rgba(104, 144, 148, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(104, 144, 148, 0.15);
}

.tag-item.active {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

/* 文章标题区域样式 */
#articles-title {
    color: var(--color-text-main);
    font-family: 'Noto Sans SC', sans-serif;
}

#articles-count {
    color: var(--color-text-secondary);
    font-family: 'Noto Sans SC', sans-serif;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .tag-cloud {
        padding: 1rem;
        gap: 0.5rem;
    }

    .tag-item {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
}

/* =============== iframe预览组件 =============== */
.preview-iframe {
    width: 100%;
    border-radius: 12px 12px 0 0 !important; /* 顶部圆角，底部完全直角 */
    pointer-events: none;
    max-width: 100%;
    aspect-ratio: 3 / 2;  /* 使用宽高比代替固定高度 */
    border: none !important;
    box-shadow: none !important; /* 移除iframe自身的阴影，让卡片统一处理 */
    display: block; /* 确保没有底部间隙 */
    margin-bottom: 0 !important; /* 确保没有底部间距 */
}

/* 置顶文章iframe样式 */
.pinned-iframe {
    border-color: var(--color-primary) !important;
    box-shadow: 0 8px 16px rgba(104, 144, 148, 0.2) !important;
    border-width: 2px !important;
}

.preview-item {
    overflow: visible; /* 改为visible，让meta区域正常显示 */
    border-radius: 0 !important; /* 强制移除外层容器的圆角 */
    box-shadow: none; /* 移除外层阴影，让内部卡片处理 */
    max-width: 800px; /* 确保最大宽度，要能够在宽屏下显示两个 */
    margin: 0 auto; /* 居中显示 */
    background: transparent !important; /* 确保背景透明 */
}

/* =============== 置顶标记组件 =============== */
.pinned-badge {
    position: absolute !important;
    top: 16px !important;
    right: 16px !important;
    background: rgba(234, 230, 225, 0.95) !important; /* 使用首页的米色背景 */
    color: var(--color-text-main) !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 1rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    z-index: 10 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
    box-shadow: 0 2px 8px rgba(61, 61, 61, 0.1) !important;
    backdrop-filter: blur(8px) !important;
    border: 1px solid rgba(104, 144, 148, 0.15) !important;
    transition: all 0.3s ease !important;
    font-family: 'Noto Sans SC', sans-serif !important;
}

.pinned-badge:hover {
    background: rgba(234, 230, 225, 1) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(61, 61, 61, 0.15) !important;
    border-color: rgba(104, 144, 148, 0.25) !important;
}

.pinned-badge i {
    font-size: 0.625rem !important;
    color: var(--color-primary) !important;
}

/* =============== iframe预览卡片样式 =============== */
.preview-card {
    background: transparent; /* 透明背景，让iframe和meta各自处理背景 */
    border-radius: 12px; /* 整体圆角 */
    overflow: hidden; /* 隐藏溢出，确保圆角效果 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* 整体阴影 */
    border: none; /* 移除边框，避免白边 */
    position: relative; /* 为渐变遮罩提供定位基础 */
}

/* 确保a标签有正确的定位属性以支持伪元素 */
.preview-card a {
    position: relative;
    display: block;
}

/* 确保iframe只有顶部圆角，底部完全直角 */
.preview-card iframe {
    border-radius: 12px 12px 0 0 !important; /* 顶部圆角，底部直角 */
    border: none !important;
    display: block;
    background: rgba(255, 255, 255, 0.9); /* iframe背景 */
    box-shadow: none; /* 移除iframe自身阴影 */
    margin: 0 !important; /* 确保没有任何间距 */
    padding: 0 !important; /* 确保没有内边距 */
    vertical-align: top; /* 防止基线对齐产生间隙 */
    border-bottom-left-radius: 0 !important; /* 强制底部左角直角 */
    border-bottom-right-radius: 0 !important; /* 强制底部右角直角 */
}

/* iframe底部渐变遮罩效果 - 基础样式 */
.preview-card a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px; /* 增加渐变高度，让过渡更自然 */
    pointer-events: none;
    z-index: 1; /* 降低z-index，确保不覆盖meta区域 */
    border-radius: 0;
    /* 默认浅色背景渐变 - 进一步降低透明度 */
    background: linear-gradient(
        to bottom,
        transparent 0%,                   /* 完全透明 */
        rgba(234, 230, 225, 0.03) 20%,   /* 极轻微遮罩 */
        rgba(234, 230, 225, 0.1) 50%,    /* 轻微遮罩 */
        rgba(234, 230, 225, 0.2) 80%,    /* 中等遮罩 */
        rgba(234, 230, 225, 0.35) 100%   /* 与meta区域透明度一致 */
    );
}

/* iframe底部渐变遮罩效果 - 深色背景适配 */
.preview-card.dark-bg a::after {
    background: linear-gradient(
        to bottom,
        transparent 0%,                   /* 完全透明 */
        rgba(0, 0, 0, 0.03) 20%,         /* 极轻微黑色遮罩 */
        rgba(0, 0, 0, 0.1) 50%,          /* 轻微黑色遮罩 */
        rgba(0, 0, 0, 0.15) 80%,         /* 中等黑色遮罩 */
        rgba(0, 0, 0, 0.25) 100%         /* 与meta区域透明度一致，让紫色渐变清晰透过 */
    ) !important;
}

/* iframe预览meta信息样式 - 基础样式 */
.preview-meta {
    background: rgba(234, 230, 225, 0.35) !important; /* 进一步降低透明度，让背景更清晰 */
    backdrop-filter: blur(8px) saturate(1.05); /* 大幅减少模糊，让背景内容清晰可见 */
    border-radius: 0 0 12px 12px !important;
    margin: 0 !important;
    border-top: none !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    position: absolute; /* 改为绝对定位 */
    bottom: 0; /* 定位到底部 */
    left: 0;
    right: 0;
    z-index: 10;
}

/* iframe预览meta信息样式 - 深色背景适配 */
.preview-card.dark-bg .preview-meta {
    background: rgba(0, 0, 0, 0.25) !important; /* 进一步降低透明度，让紫色渐变清晰透过 */
    backdrop-filter: blur(8px) saturate(1.05); /* 大幅减少模糊，让背景内容清晰可见 */
}

/* 深色背景下的文字颜色调整 */
.preview-card.dark-bg .preview-meta .font-context,
.preview-card.dark-bg .preview-meta .fa-calendar-alt {
    color: rgba(255, 255, 255, 0.8) !important; /* 浅色文字，提高对比度 */
}

.preview-meta .font-context {
    font-family: 'Noto Sans SC', sans-serif;
    font-weight: normal !important; /* 确保日期不是粗体 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    .preview-meta {
        padding: 0.5rem 0.75rem !important; /* 减少移动端内边距 */
    }

    /* 移动端保持水平布局，但调整间距和大小 */
    .preview-meta > .flex {
        flex-direction: row !important; /* 保持水平排列 */
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0.25rem !important;
        flex-wrap: nowrap !important; /* 不允许换行 */
        padding: 0.5rem 0.75rem !important; /* 覆盖内联样式，保持与py-2一致 */
    }

    .preview-meta > .flex > .flex:first-child {
        flex-shrink: 0; /* 日期部分不收缩 */
        min-width: fit-content;
        gap: 0.25rem !important;
    }

    .preview-meta > .flex > .flex:last-child {
        flex-shrink: 1; /* 标签部分可以收缩 */
        justify-content: flex-end !important;
        align-items: center !important;
        overflow: hidden; /* 隐藏溢出的标签 */
    }

    /* 移动端标签样式优化 */
    .preview-meta .flex-wrap {
        gap: 0.125rem !important; /* 进一步减少标签间距 */
        justify-content: flex-end !important;
        flex-wrap: wrap !important;
        max-width: 60% !important; /* 限制标签区域最大宽度 */
    }

    .preview-meta span[class*="bg-"] {
        font-size: 0.5rem !important; /* 更小的字体 */
        padding: 0.125rem 0.25rem !important; /* 更小的内边距 */
        white-space: nowrap; /* 防止标签文字换行 */
        flex-shrink: 0; /* 标签不收缩 */
    }

    /* 移动端iframe高度调整 */
    .preview-iframe {
        aspect-ratio: 2.5 / 2 !important; /* 移动端使用更紧凑的比例 */
    }

    /* 移动端渐变遮罩调整 */
    .preview-card a::after {
        height: 60px; /* 移动端减少渐变区域高度 */
    }

    /* 移动端日期文字优化 */
    .preview-meta .font-context {
        font-size: 0.625rem !important;
        white-space: nowrap; /* 防止日期换行 */
    }

    /* 移动端图标优化 */
    .preview-meta .fa-calendar-alt {
        font-size: 0.625rem !important;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .preview-meta {
        padding: 0.375rem 0.5rem !important;
    }

    .preview-meta .flex {
        gap: 0.25rem !important;
    }

    .preview-meta span[class*="bg-"] {
        font-size: 0.45rem !important;
        padding: 0.1rem 0.2rem !important;
    }

    .preview-meta .font-context {
        font-size: 0.55rem !important;
    }

    .preview-meta .fa-calendar-alt {
        font-size: 0.55rem !important;
        margin-right: 0.125rem !important;
    }
}

/* =============== 文章卡片组件 =============== */
.article-card {
    display: block;
}

.article-card-inner {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
}

/* =============== 通用卡片组件 =============== */
.card {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}
.card-header {
    padding: 1.5rem;
    color: white;
}
.card-body {
    padding: 1.5rem;
    background-color: white;
    flex-grow: 1;
}
.card-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}
.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}
.card-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}
.card-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}
.card-list-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}
.card-list-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.card-list-icon {
    margin-right: 0.75rem;
    color: var(--color-primary);
    flex-shrink: 0;
    margin-top: 0.25rem;
}

/* =============== 标签页面组件 =============== */
.tags-gradient-bg {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
}
.tags-card {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}
.tags-card-header {
    padding: 1.5rem;
    color: white;
}
.tags-card-body {
    padding: 1.5rem;
    background-color: white;
    flex-grow: 1;
}
.tags-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

/* 标签云样式 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
}

.tag-item {
    display: inline-block;
    padding: 0.2rem 0.5rem; /* 更小 */
    border-radius: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-width: 1px;
    border-style: solid;
    font-size: 0.5rem; /* 更小 */
    border-color: var(--color-primary);
    font-weight: 500;
    text-decoration: none;
    background: transparent;
    color: var(--color-primary);
 }

 .tag-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: rgba(var(--color-primary-rgb),0.08);
 }

 .tag-item.active {
    border-color: var(--color-primary);
    background-color: var(--color-primary);
    color: #fff;
 }

/* 标签大小等级 */
.tag-size-1 { font-size: 0.7rem; }
.tag-size-2 { font-size: 0.75rem; }
.tag-size-3 { font-size: 0.8rem; }
.tag-size-4 { font-size: 0.85rem; }
.tag-size-5 { font-size: 0.9rem; }

/* 文章标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-decoration: none;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

/* 置顶徽章 */
.pinned-badge {
    background-color: #f59e0b;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
}
.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #edf2f7;
}
.feature-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.feature-icon {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    color: white;
    font-size: 1.25rem;
}

/* ===== Pagination 自适应 ===== */
.pagination-wrapper{
    display:flex;
    justify-content:center;
    align-items:center;
    gap:0.75rem;
}
.pagination-btn{
    background:transparent;
    border:none;
    font-size:1.25rem;
    cursor:pointer;
    color:var(--color-text-main);
    transition:color 0.2s;
}
.pagination-btn:hover:not(:disabled){
    color:var(--color-primary);
}
.pagination-btn-disabled{
    color:var(--color-text-secondary);
    cursor:default;
}
.page-info{
    font-family:'Noto Sans SC',sans-serif;
}
@media(max-width:640px){
    .page-info::after{content:attr(data-compact);}
    .page-info>span{display:none;}
    .pagination-wrapper{gap:0.5rem;}
    .pagination-btn{font-size:1rem;}
}
