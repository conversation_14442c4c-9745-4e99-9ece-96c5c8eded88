<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有谁在，就算盛大</title>
    
    <!-- TailwindCSS 3.4.1 via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome 6.5.1 via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Roboto:wght@300;400&display=swap');
        
        body {
            font-family: 'Noto Sans SC', 'Roboto', sans-serif;
            background-color: #0A0A0C; /* 更深邃的背景 */
            color: #EAEAEA;
            overflow-x: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 1.75rem; /* 对应 rounded-3xl */
            border: 1px solid transparent;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0)) border-box;
            -webkit-mask: 
                linear-gradient(#fff 0 0) content-box, 
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }
        
        #blurry-gradient-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            filter: blur(140px); /* 更强的模糊效果，更柔和的光 */
            overflow: hidden;
        }

        .blob {
            position: absolute;
            border-radius: 50%;
            opacity: 0.5;
            transition: all 8s ease-in-out;
        }

        /* 简单的淡入动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 1.5s ease-out forwards;
        }
        .fade-in-delay-1 { animation-delay: 0.5s; opacity: 0; }
        .fade-in-delay-2 { animation-delay: 1.0s; opacity: 0; }
        .fade-in-delay-3 { animation-delay: 1.5s; opacity: 0; }
    </style>
</head>
<body class="antialiased">

    <!-- 动态光晕背景 -->
    <div id="blurry-gradient-bg"></div>

    <!-- 主内容容器 -->
    <div class="relative min-h-screen w-full mx-auto px-6 py-20 md:px-12 md:py-28 lg:px-24">

        <!-- Section 1: 开篇宣告 -->
        <section class="min-h-[70vh] flex flex-col justify-center text-center">
            <h1 class="fade-in text-7xl sm:text-8xl md:text-9xl font-black tracking-tighter leading-none">
                不必等了。
            </h1>
            <p class="fade-in fade-in-delay-1 mt-6 text-lg md:text-xl text-gray-500 font-light tracking-[0.3em] uppercase">THE WAITING IS OVER</p>
        </section>

        <!-- Section 2: 核心阐述 -->
        <section class="mt-16 lg:mt-24">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- 卡片 1: 圆满是神话 -->
                <div class="relative glass-card bg-white/5 backdrop-blur-2xl rounded-3xl p-8 md:p-12 h-full fade-in fade-in-delay-2">
                    <h2 class="text-5xl md:text-6xl font-bold">圆满是神话</h2>
                    <p class="mt-2 text-sm text-gray-400 uppercase tracking-widest">Perfection is a Myth</p>
                    
                    <div class="absolute bottom-8 right-8 text-sky-400/30">
                        <svg class="w-24 h-24" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M 90 50 A 40 40 0 1 1 50 10" stroke="url(#grad1)" stroke-width="3" stroke-linecap="round"/>
                           <defs>
                                <linearGradient id="grad1">
                                    <stop offset="0%" stop-color="#38BDF8" stop-opacity="0.8" />
                                    <stop offset="100%" stop-color="#38BDF8" stop-opacity="0" />
                                </linearGradient>
                           </defs>
                        </svg>
                    </div>
                </div>

                <!-- 卡片 2: 相逢是刹那 -->
                <div class="relative glass-card bg-white/5 backdrop-blur-2xl rounded-3xl p-8 md:p-12 h-full fade-in fade-in-delay-2">
                    <h2 class="text-5xl md:text-6xl font-bold">相逢是刹那</h2>
                    <p class="mt-2 text-sm text-gray-400 uppercase tracking-widest">Encounters are Instantaneous</p>

                    <div class="absolute bottom-8 right-8 text-pink-500/30">
                        <svg class="w-24 h-24" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M 15 85 L 85 15" stroke="url(#grad2)" stroke-width="3" stroke-linecap="round"/>
                            <path d="M 45 15 L 85 55" stroke="url(#grad2)" stroke-width="1.5" stroke-linecap="round" stroke-dasharray="4 8"/>
                            <defs>
                                <linearGradient id="grad2">
                                    <stop offset="0%" stop-color="#EC4899" stop-opacity="0" />
                                    <stop offset="100%" stop-color="#EC4899" stop-opacity="0.8" />
                                </linearGradient>
                           </defs>
                        </svg>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: 最终升华 -->
        <section class="mt-28 lg:mt-40 text-center max-w-5xl mx-auto fade-in fade-in-delay-3">
            <div class="mb-12 flex justify-center">
                <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="35" cy="50" r="8" fill="url(#grad_presence_1)"/>
                    <circle cx="65" cy="50" r="12" fill="url(#grad_presence_2)"/>
                    <path d="M43 50 Q 50 42 57 50" stroke="rgba(234, 234, 234, 0.2)" stroke-width="1.5" stroke-linecap="round" />
                    <defs>
                        <radialGradient id="grad_presence_1" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                            <stop offset="0%" style="stop-color:rgb(56,189,248);stop-opacity:1" />
                            <stop offset="100%" style="stop-color:rgb(56,189,248);stop-opacity:0.3" />
                        </radialGradient>
                         <radialGradient id="grad_presence_2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                            <stop offset="0%" style="stop-color:rgb(236,72,153);stop-opacity:1" />
                            <stop offset="100%" style="stop-color:rgb(236,72,153);stop-opacity:0.3" />
                        </radialGradient>
                    </defs>
                </svg>
            </div>
            <h3 class="text-6xl md:text-7xl lg:text-8xl font-black tracking-tight leading-tight">
                有谁在，<br class="md:hidden">就算
                <span class="text-transparent bg-clip-text bg-gradient-to-br from-sky-400 to-pink-500">盛大</span>。
            </h3>
            <p class="mt-5 text-base md:text-lg text-gray-500 font-light tracking-[0.3em] uppercase">PRESENCE IS GRANDEUR</p>
        </section>
        
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const bgContainer = document.getElementById('blurry-gradient-bg');
            const colors = ['#38BDF8', '#EC4899', '#34D399', '#A78BFA']; // 深空蓝, 星云紫/粉, 绿...
            const blobCount = 4;

            for (let i = 0; i < blobCount; i++) {
                const blob = document.createElement('div');
                blob.classList.add('blob');
                blob.style.backgroundColor = colors[i % colors.length];
                bgContainer.appendChild(blob);
                moveBlob(blob);
            }

            function moveBlob(blob) {
                const size = Math.random() * 250 + 200; // 200px to 450px
                const x = Math.random() * (window.innerWidth - size);
                const y = Math.random() * (document.body.scrollHeight - size); // 在整个页面高度内移动
                const duration = Math.random() * 15 + 15; // 15s to 30s, 更慢的移动

                blob.style.width = `${size}px`;
                blob.style.height = `${size}px`;
                blob.style.transform = `translate(${x}px, ${y}px)`;
                blob.style.transitionDuration = `${duration}s`;
                
                setTimeout(() => moveBlob(blob), duration * 1000);
            }
            
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    const blobs = document.querySelectorAll('.blob');
                    blobs.forEach(blob => {
                        // 强制重绘并重新移动
                        blob.style.transitionDuration = '0s';
                        moveBlob(blob);
                    });
                }, 250);
            });
        });
    </script>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>