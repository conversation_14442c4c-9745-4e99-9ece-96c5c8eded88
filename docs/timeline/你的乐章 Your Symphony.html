<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你的乐章 | Your Symphony</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    
    <!-- Font Awesome for Icons via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Fonts: Noto Sans SC for Chinese -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Inter:wght@400;700;900&display=swap" rel="stylesheet">

    <style>
        /* --- 自定义全局样式 --- */
        body {
            /* 
             * 主体使用 Inter 字体以获得更好的西文展示效果和数字清晰度, 
             * Noto Sans SC 作为中文字体回退，确保中文显示优美。
             */
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: #f9fafb; /* 极浅的灰色背景，比纯白更柔和 */
            color: #1f2937; /* 深灰色文字，保证对比度和可读性 */
            overflow-x: hidden;
        }

        /* --- 渐变高亮色定义 --- */
        .gradient-text-vibrant {
            background-image: linear-gradient(120deg, #3b82f6 0%, #a855f7 50%, #ec4899 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .gradient-bg-vibrant {
            background-image: linear-gradient(120deg, #3b82f6 0%, #a855f7 100%);
        }
        
        .gradient-bg-accent {
             background-image: linear-gradient(120deg, #8b5cf6 0%, #ec4899 100%);
        }

        /* --- 滚动入场动画 --- */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1.2s cubic-bezier(0.16, 1, 0.3, 1), transform 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            transition-delay: 0.2s;
        }

        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* --- 装饰性辉光元素 --- */
        .glow-element {
            position: absolute;
            border-radius: 9999px;
            filter: blur(128px); /* 大范围高斯模糊创造辉光 */
            z-index: -10;
        }

        /* --- 简洁勾线SVG样式 --- */
        .outline-graphic {
            stroke: #9ca3af; /* 中灰色描边 */
            stroke-width: 1.5;
            transition: all 0.5s ease-in-out;
        }

    </style>
</head>
<body class="antialiased">

    <!-- 主容器 -->
    <main class="relative z-10">

        <!-- Section 1: 核心概念 - 你的生命，只是这一个音 -->
        <section class="min-h-screen flex items-center justify-center p-8 overflow-hidden">
            <!-- 背景辉光装饰 -->
            <div class="glow-element w-[500px] h-[500px] bg-blue-400/30 top-1/4 left-1/4 -translate-x-1/2 -translate-y-1/2"></div>
            
            <div class="text-center scroll-reveal">
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mb-4">YOUR LIFE</p>
                <h1 class="text-6xl md:text-9xl font-black tracking-tight leading-tight">
                    你的生命<br>只是<span class="gradient-text-vibrant">这一个音</span>
                </h1>
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mt-6">JUST ONE NOTE</p>
            </div>
        </section>

        <!-- Section 2: 昨天与明天 - 回音与天籁 -->
        <section class="py-24 md:py-40">
            <div class="max-w-7xl mx-auto px-8">
                <div class="grid md:grid-cols-2 gap-16 md:gap-24 items-center">
                    
                    <!-- 左侧：昨天是回音 -->
                    <div class="text-center md:text-left scroll-reveal">
                        <div class="flex justify-center md:justify-start mb-8">
                           <svg width="120" height="120" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle class="outline-graphic" cx="50" cy="50" r="15" style="opacity: 1;"></circle>
                                <circle class="outline-graphic" cx="50" cy="50" r="30" style="opacity: 0.6;"></circle>
                                <circle class="outline-graphic" cx="50" cy="50" r="45" style="opacity: 0.3;"></circle>
                           </svg>
                        </div>
                        <h2 class="text-5xl md:text-7xl font-black tracking-tight">别看昨天</h2>
                        <p class="text-4xl md:text-5xl font-bold text-gray-400 mt-2">那是回音</p>
                        <p class="text-xl font-bold text-gray-400 tracking-widest mt-4">THE PAST IS AN ECHO</p>
                    </div>

                    <!-- 右侧：明天是天籁 -->
                    <div class="text-center md:text-right scroll-reveal" style="transition-delay: 0.4s;">
                        <div class="flex justify-center md:justify-end mb-8">
                            <svg width="120" height="120" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 75 L35 60 L50 70 L65 45 L80 55" class="outline-graphic" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M20 75 L80 75" class="outline-graphic" stroke-dasharray="4 4" style="stroke: #d1d5db;"></path>
                                <path d="M20 25 L20 75" class="outline-graphic" stroke-dasharray="4 4" style="stroke: #d1d5db;"></path>
                            </svg>
                        </div>
                        <h2 class="text-5xl md:text-7xl font-black tracking-tight">别想明天</h2>
                        <p class="text-4xl md:text-5xl font-bold text-gray-400 mt-2">那是天籁</p>
                        <p class="text-xl font-bold text-gray-400 tracking-widest mt-4">THE FUTURE, A PROMISE</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: 成为它 - 黑暗模式对比区块 -->
        <section class="bg-gray-900 text-gray-200 py-24 md:py-48 my-20 md:my-32 rounded-[48px] mx-4 md:mx-auto md:max-w-7xl overflow-hidden">
             <!-- 背景辉光装饰 -->
            <div class="relative">
                 <div class="glow-element w-[600px] h-[600px] bg-purple-600/20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            </div>
            <div class="max-w-4xl mx-auto px-8 text-center scroll-reveal">
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mb-4">WHOLLY</p>
                <h2 class="text-6xl md:text-9xl font-black tracking-tight leading-tight text-white">全然地<br><span class="gradient-text-vibrant">成为它</span></h2>
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mt-6">BECOME IT</p>
                 <!-- 透明度渐变装饰线 -->
                <div class="w-1/2 h-1 mx-auto mt-16 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
            </div>
        </section>

        <!-- Section 4: 终章 - 你的乐章 -->
        <section class="min-h-screen flex items-center justify-center p-8 overflow-hidden">
            <div class="relative text-center scroll-reveal">
                 <!-- 背景辉光装饰 -->
                <div class="glow-element w-[600px] h-[600px] bg-pink-400/30 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            
                <div class="mb-12">
                     <svg width="120" height="120" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                        <defs>
                            <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3b82f6;" />
                                <stop offset="100%" style="stop-color:#ec4899;" />
                            </linearGradient>
                        </defs>
                        <path d="M18 10V15.3536C18 17.9258 15.9258 20 13.3536 20C10.7813 20 8.70711 17.9258 8.70711 15.3536C8.70711 13.2952 10.0693 11.5833 12 11.0556V16M12 4L12 11.0556C10.0693 11.5833 8.70711 13.2952 8.70711 15.3536M18 10C18 7.23858 15.3137 5 12 5" stroke="url(#iconGradient)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                     </svg>
                </div>

                <h2 class="text-6xl md:text-9xl font-black tracking-tight">这，就是<br>你的<span class="gradient-text-vibrant">乐章</span></h2>
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mt-6">THIS, IS YOUR SYMPHONY</p>
                
                <div class="mt-16">
                    <a href="#" class="inline-block px-10 py-5 text-xl font-bold text-white rounded-2xl shadow-2xl shadow-purple-500/20 transition-transform duration-300 hover:scale-105 gradient-bg-accent">
                        开启新篇章 <i class="fa-solid fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="text-center py-12">
            <p class="text-gray-400">Designed with a focus on the present moment.</p>
        </footer>

    </main>

    <script>
        // --- 简单的滚动入场动画脚本 ---
        document.addEventListener("DOMContentLoaded", function() {
            const reveals = document.querySelectorAll('.scroll-reveal');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        // 动画只触发一次，触发后停止观察
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1 // 元素进入视口10%时触发
            });

            reveals.forEach(reveal => {
                observer.observe(reveal);
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>