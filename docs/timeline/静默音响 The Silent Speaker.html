<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静默音响 | The Silent Speaker</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    
    <!-- Font Awesome for Icons via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Fonts: Noto Sans SC & Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Inter:wght@400;700;900&display=swap" rel="stylesheet">

    <style>
        /* --- 自定义全局样式 --- */
        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: #f9fafb; /* 极浅灰色背景 */
            color: #1f2937; /* 深灰色文字 */
            overflow-x: hidden;
        }

        /* --- 渐变高亮色定义 --- */
        .gradient-text-vibrant {
            background-image: linear-gradient(120deg, #3b82f6 0%, #a855f7 50%, #ec4899 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg-accent {
             background-image: linear-gradient(120deg, #8b5cf6 0%, #ec4899 100%);
        }

        /* --- 滚动入场动画 --- */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1.2s cubic-bezier(0.16, 1, 0.3, 1), transform 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            transition-delay: 0.2s;
        }

        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* --- 装饰性辉光元素 --- */
        .glow-element {
            position: absolute;
            border-radius: 9999px;
            filter: blur(160px); /* 更大的模糊创造更柔和的辉光 */
            z-index: -10;
            pointer-events: none; /* 确保辉光不会捕获鼠标事件 */
        }

        /* --- 简洁勾线SVG样式 --- */
        .outline-graphic {
            stroke: #9ca3af; /* 中灰色描边 */
            stroke-width: 1;
            transition: all 0.5s ease-in-out;
            fill: none;
        }

    </style>
</head>
<body class="antialiased">

    <!-- 主容器 -->
    <main class="relative z-10">

        <!-- Section 1: 问题 - 坏掉的收音机 -->
        <section class="min-h-screen flex flex-col items-center justify-center p-8 overflow-hidden">
            <!-- 背景辉光装饰 -->
            <div class="glow-element w-[400px] h-[400px] bg-amber-400/20 top-1/3 left-1/4"></div>
            <div class="glow-element w-[300px] h-[300px] bg-red-400/20 bottom-1/3 right-1/4"></div>
            
            <div class="text-center scroll-reveal">
                <!-- 混乱的勾线图形，代表“串台” -->
                <svg width="150" height="100" viewBox="0 0 150 100" class="mx-auto mb-8 opacity-50">
                    <path d="M10 20 C 40 80, 70 -20, 100 50 S 140 0, 140 80" class="outline-graphic" />
                    <path d="M20 80 C 50 10, 80 120, 110 30 S 130 100, 150 20" class="outline-graphic" />
                    <path d="M5 50 C 35 50, 60 50, 90 50 L 145 50" class="outline-graphic" stroke-dasharray="2 4" stroke-width="1.5" />
                </svg>
                <h1 class="text-6xl md:text-8xl font-black tracking-tight">你的心，是一台<br>坏掉的<span class="gradient-text-vibrant">收音机</span></h1>
                <p class="text-3xl md:text-5xl font-bold text-gray-400 mt-4">永远在串台</p>
                <p class="text-xl md:text-2xl font-bold text-gray-400 tracking-widest mt-6">A BROKEN RADIO, ALWAYS CROSS-TALKING</p>
            </div>
        </section>

        <!-- Section 2: 目的 - 静默的音响 -->
        <section class="py-24 md:py-40">
            <div class="max-w-4xl mx-auto px-8 text-center scroll-reveal">
                <p class="text-2xl md:text-3xl font-bold text-gray-500 tracking-widest mb-4">THE PURPOSE OF PRACTICE</p>
                <h2 class="text-5xl md:text-7xl font-black tracking-tight">修行的目的<br>是让它成为一台<span class="text-gray-800">静默的音响</span></h2>
                 <!-- 简洁的音响图形 -->
                 <svg width="120" height="120" viewBox="0 0 100 100" class="mx-auto mt-12 opacity-80">
                    <rect x="20" y="10" width="60" height="80" rx="8" class="outline-graphic" stroke-width="1.5"/>
                    <circle cx="50" cy="40" r="18" class="outline-graphic" stroke-width="1.5"/>
                    <circle cx="50" cy="40" r="4" class="outline-graphic" style="fill: #9ca3af;"/>
                 </svg>
            </div>
        </section>

        <!-- Section 3: 方法与目标 - 深色聚焦区块 -->
        <section class="bg-gray-900 text-gray-200 py-24 md:py-32 my-20 md:my-32 rounded-[48px] mx-4 md:mx-auto md:max-w-7xl overflow-hidden relative">
            <!-- 背景辉光装饰 -->
            <div class="glow-element w-[700px] h-[700px] bg-blue-600/25 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            
            <div class="relative z-10 max-w-7xl mx-auto px-8 grid md:grid-cols-2 gap-16 md:gap-24 items-center">
                <!-- 左侧：方法 -->
                <div class="text-center md:text-left scroll-reveal">
                    <p class="text-xl md:text-2xl font-bold text-gray-500 tracking-widest mb-4">方法 METHOD</p>
                    <i class="fa-solid fa-volume-xmark text-8xl md:text-9xl mb-6 gradient-text-vibrant"></i>
                    <h3 class="text-5xl md:text-6xl font-black text-white">按下“静音键”</h3>
                    <p class="text-3xl md:text-4xl font-bold text-gray-400 mt-2">(冥想)</p>
                </div>

                <!-- 右侧：目的 -->
                <div class="text-center md:text-left scroll-reveal" style="transition-delay: 0.4s;">
                    <p class="text-xl md:text-2xl font-bold text-gray-500 tracking-widest mb-4">目的 PURPOSE</p>
                    <h3 class="text-4xl md:text-5xl font-bold leading-tight text-white">
                        听见那首被噪音掩盖<br>名为「<span class="gradient-text-vibrant font-black">自性</span>」的歌
                    </h3>
                    <!-- 纯净的声波图形 -->
                    <svg width="100%" height="40" viewBox="0 0 200 40" class="mt-8">
                        <path d="M0 20 Q 25 0, 50 20 T 100 20 T 150 20 T 200 20" stroke="#a855f7" stroke-width="2.5" fill="none" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </section>

        <!-- Section 4: 结果 - 自由选择 -->
        <section class="min-h-screen flex flex-col items-center justify-center p-8 overflow-hidden">
             <!-- 背景辉光装饰 -->
            <div class="glow-element w-[600px] h-[600px] bg-pink-400/30 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></div>
            
            <div class="text-center scroll-reveal">
                <p class="text-3xl md:text-5xl font-bold text-gray-400 mb-2">然后</p>
                <p class="text-xl md:text-2xl font-bold text-gray-400 tracking-widest mb-8">AND THEN</p>
                
                <h2 class="text-6xl md:text-8xl font-black tracking-tight">
                    自由地，选择<br>你想播放的<span class="gradient-text-vibrant">下一首曲子</span>
                </h2>
                
                <div class="mt-16">
                    <a href="#" class="inline-block px-10 py-5 text-xl font-bold text-white rounded-2xl shadow-2xl shadow-purple-500/30 transition-transform duration-300 hover:scale-105 gradient-bg-accent">
                        创建我的播放列表 <i class="fa-solid fa-music ml-2"></i>
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="text-center py-12">
            <p class="text-gray-400">Find your silence. Curate your symphony.</p>
        </footer>

    </main>

    <script>
        // --- 简单的滚动入场动画脚本 ---
        document.addEventListener("DOMContentLoaded", function() {
            const reveals = document.querySelectorAll('.scroll-reveal');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        // 动画只触发一次，触发后停止观察
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15 // 元素进入视口15%时触发
            });

            reveals.forEach(reveal => {
                observer.observe(reveal);
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>