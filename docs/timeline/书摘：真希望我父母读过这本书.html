<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>读书感悟：《真希望我父母读过这本书》</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="../../css/styles.css">
    <style>
        body {
            font-family: 'system-ui', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #FFFAF0; /* 柔和的米白色背景 */
            color: #4A4A4A; /* 深灰色文字 */
        }
        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem); /* 响应式超大字体 */
            font-weight: 700;
            line-height: 1.2;
            color: #5D4037; /* 暖棕色 */
        }
        .hero-subtitle {
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            color: #795548; /* 稍浅的棕色 */
        }
        .section-title {
            font-size: clamp(1.8rem, 4vw, 3rem);
            font-weight: 600;
            color: #6D4C41; /* 棕色系标题 */
            margin-bottom: 1rem;
        }
         .point-title {
            font-size: clamp(1.5rem, 3vw, 2.2rem);
            font-weight: 600;
            color: #3E2723; /* 深棕色 */
        }
        .point-number {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            color: #A1887F; /* 浅棕色数字，增加透明度 */
            opacity: 0.6;
        }
        .highlight-text {
            color: #8D6E63; /* 强调色 */
            font-weight: 600;
        }
        .icon-container { /* Replaces illustration-container */
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #A1887F; /* Default icon color */
            min-height: 120px; /* Adjusted for icons */
        }
        .icon-container i { /* Style for Font Awesome icons */
            font-size: clamp(3rem, 8vw, 5rem); /* Responsive icon size */
            color: #795548; /* Main icon color - Brown */
        }
        .card {
            background-color: #FFF8E1; /* 淡黄色卡片 */
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            overflow: hidden;
        }
    </style>
</head>
<body class="leading-relaxed">

    <!-- Hero Section -->
    <header class="min-h-screen flex flex-col justify-center items-center text-center py-12 px-4 md:px-8 bg-gradient-to-b from-[#FFF3E0] to-[#FFFAF0]">
        <div class="w-full max-w-4xl">
            <div class="icon-container w-full h-64 md:h-80 mb-8 rounded-lg">
                <i class="fa-solid fa-people-roof fa-beat" style="--fa-animation-duration: 2s; font-size: clamp(5rem, 15vw, 10rem); color: #8D6E63;"></i>
            </div>
            <h1 class="hero-title mb-4">真希望我父母<br class="md:hidden">读过这本书</h1>
            <p class="hero-subtitle mb-2">读书总结与感悟</p>
            <p class="text-lg md:text-xl text-[#A1887F]">—— 你的孩子也会庆幸你读过</p>
        </div>
    </header>

    <main class="container mx-auto px-4 md:px-8 py-12 md:py-20 max-w-6xl space-y-16 md:space-y-24">

        <!-- 引言/本书简介 -->
        <section class="text-center">
            <h2 class="section-title mb-6"><i class="fa-solid fa-book-open-reader mr-3 text-[#8D6E63] text-3xl"></i>关于这本书</h2>
            <p class="text-lg md:text-xl max-w-3xl mx-auto text-gray-700">
                《真希望我父母读过这本书》是一盏明灯，照亮了亲子关系中那些常被忽略的角落。它不仅仅是一本育儿指南，更是一次深刻的自我探索之旅，引导我们理解孩子的情感世界，疗愈过往的印记，并与孩子共同成长。
            </p>
            <div class="mt-8 icon-container w-48 h-32 mx-auto rounded-md">
                <i class="fa-solid fa-magnifying-glass"></i>
            </div>
        </section>

        <!-- 核心观点提炼 -->
        <section>
            <h2 class="section-title text-center mb-12 md:mb-16">核心启示录 <span class="text-sm align-top text-[#BCAAA4]">KEY INSIGHTS</span></h2>

            <div class="grid md:grid-cols-2 gap-8 md:gap-12 items-start">
                <!-- 观点1 -->
                <div class="card p-6 md:p-8">
                    <div class="flex items-start mb-4">
                        <span class="point-number mr-4">01</span>
                        <h3 class="point-title mt-3">情感的港湾：<br>理解与接纳孩子的所有情绪</h3>
                    </div>
                    <div class="icon-container h-40 mb-6 rounded-md">
                        <i class="fa-solid fa-heart-circle-check"></i>
                    </div>
                    <p class="text-gray-600 text-base md:text-lg">
                        书中强调，父母需要成为孩子安全的“情感容器”。不是压制或否定孩子的情绪，而是<span class="highlight-text">共情他们的感受</span>，帮助他们识别、命名并健康地表达喜怒哀乐。这为孩子建立强大的心理韧性奠定基础。
                    </p>
                </div>

                <!-- 观点2 -->
                <div class="card p-6 md:p-8 mt-8 md:mt-0">
                     <div class="flex items-start mb-4">
                        <span class="point-number mr-4">02</span>
                        <h3 class="point-title mt-3">镜子与回响：<br>父母的自我觉察与成长</h3>
                    </div>
                    <div class="icon-container h-40 mb-6 rounded-md">
                         <i class="fa-solid fa-brain"></i>
                    </div>
                    <p class="text-gray-600 text-base md:text-lg">
                        我们的育儿方式深受自身成长经历的影响。这本书鼓励父母<span class="highlight-text">勇敢地回溯和疗愈</span>自己内心的创伤和未满足的需求。只有先照顾好自己，才能给予孩子更稳定、更滋养的爱。
                    </p>
                </div>

                <!-- 观点3 -->
                 <div class="card p-6 md:p-8 mt-8 md:mt-0 md:col-span-2 lg:col-span-1">
                     <div class="flex items-start mb-4">
                        <span class="point-number mr-4">03</span>
                        <h3 class="point-title mt-3">温柔的界限：<br>爱与规则如何并行不悖</h3>
                    </div>
                    <div class="icon-container h-40 mb-6 rounded-md">
                        <i class="fa-solid fa-shield-heart"></i>
                    </div>
                    <p class="text-gray-600 text-base md:text-lg">
                        爱不等于溺爱，规则也不意味着冷酷。书中提倡在充满爱的基础上，为孩子设立<span class="highlight-text">清晰、一致且合理的界限</span>。这能给孩子带来安全感，并帮助他们学会尊重他人和规则。
                    </p>
                </div>

                <!-- 观点4 -->
                <div class="card p-6 md:p-8 mt-8 md:mt-0 md:col-span-2 lg:col-span-1">
                     <div class="flex items-start mb-4">
                        <span class="point-number mr-4">04</span>
                        <h3 class="point-title mt-3">修复的艺术：<br>没有完美父母，只有成长关系</h3>
                    </div>
                    <div class="icon-container h-40 mb-6 rounded-md">
                        <i class="fa-solid fa-link-slash fa-rotate-90"></i> <i class="fa-solid fa-link ml-2"></i>
                    </div>
                    <p class="text-gray-600 text-base md:text-lg">
                        亲子间难免会有冲突和误解。重要的不是避免犯错，而是<span class="highlight-text">真诚地道歉和努力修复关系</span>。每一次成功的修复，都会加深彼此的信任和情感联结。
                    </p>
                </div>
            </div>
        </section>

        <!-- 我的深刻感悟与行动 -->
        <section class="bg-[#FFFDE7] p-8 md:p-12 rounded-xl shadow-lg">
            <h2 class="section-title text-center mb-8"><i class="fa-solid fa-seedling mr-3 text-[#8D6E63] text-3xl"></i>我的感悟：育儿即育己，爱是双向流动</h2>
            <div class="flex flex-col md:flex-row items-center gap-8">
                <div class="md:w-1/3 icon-container h-56 rounded-lg">
                    <i class="fa-solid fa-spa"></i>
                </div>
                <div class="md:w-2/3">
                    <p class="text-lg md:text-xl text-gray-700 mb-4">
                        阅读这本书的过程，像是一面镜子，不断映照出我为人父母的焦虑、困惑以及内心深处的渴望。最大的触动是认识到，<span class="highlight-text">想要更好地爱孩子，必先疗愈和丰盈自己</span>。不是要成为“完美”的父母，而是成为一个“真实”且不断学习和成长的父母。
                    </p>
                    <p class="text-lg md:text-xl text-gray-700">
                        我开始尝试放下预设的期待，<span class="highlight-text">真正地去倾听和观察我的孩子</span>，理解他们行为背后的需求和情感。未来，我希望在家庭中营造更开放、更包容的沟通氛围，让爱在我们之间自由流动，彼此滋养。
                    </p>
                </div>
            </div>
        </section>

        <!-- 结语/推荐 -->
        <footer class="text-center py-12 border-t-2 border-dashed border-[#D7CCC8] mt-16 md:mt-24">
            <div class="icon-container w-40 h-40 mx-auto mb-8 rounded-full">
                 <i class="fa-solid fa-hands-holding-child"></i>
            </div>
            <p class="text-xl md:text-2xl font-semibold text-[#6D4C41] mb-4">
                愿我们都能成为更好的父母，也成为更好的自己。
            </p>
            <p class="text-gray-600 text-base md:text-lg max-w-2xl mx-auto">
                这本书是送给所有父母和准父母的珍贵礼物。如果你渴望建立更深厚、更健康的亲子关系，强烈推荐你阅读。
                <i class="fa-solid fa-gift ml-2 text-[#8D6E63]"></i>
            </p>
            <p class="mt-8 text-sm text-gray-500">
                动态网页由AI助手生成 © <span id="year"></span>
            </p>
        </footer>
    </main>

    <script>
        document.getElementById('year').textContent = new Date().getFullYear();

        // Simple smooth scroll for potential anchor links (if added later)
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>