<!DOCTYPE html>
<html lang="zh-CN" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI阴影下的生存指南</title>
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --system-blue: 0 122 255;
            --knowledge-purple: 88 86 214;
            --action-green: 52 199 89;
            --aurora-gradient: linear-gradient(90deg, #38bdf8, #818cf8, #f472b6);
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: #f9fafb;
            color: #111827;
        }

        html.dark body {
            background-color: #0d1117;
            color: #f9fafb;
        }

        .text-aurora {
            background: var(--aurora-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.7s cubic-bezier(0.33, 1, 0.68, 1), transform 0.7s cubic-bezier(0.33, 1, 0.68, 1);
        }

        .is-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 { transition-delay: 100ms; }
        .delay-2 { transition-delay: 200ms; }
        .delay-3 { transition-delay: 300ms; }
        .delay-4 { transition-delay: 400ms; }

        .dark .card-glass {
            background-color: rgba(28, 41, 60, 0.5);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="antialiased">

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32 2xl:max-w-screen-2xl">

        <!-- Hero Section -->
        <section class="text-center mb-24 md:mb-32">
            <h1 class="fade-in-up text-6xl md:text-8xl lg:text-9xl font-black text-aurora">
                时代换挡
            </h1>
            <p class="fade-in-up delay-1 mt-6 text-2xl md:text-3xl font-bold tracking-tight max-w-4xl mx-auto text-gray-800 dark:text-gray-200">
                黄仁勋说别学编程后，我们到底该学什么？
            </p>
            <p class="fade-in-up delay-2 mt-8 text-base md:text-lg max-w-3xl mx-auto text-gray-600 dark:text-gray-400">
                这不仅是程序员的生存危机，这是AI时代投向所有知识工作者的巨大阴影。以下是穿越这场风暴的三张核心地图。
            </p>
        </section>

        <!-- The Three Laws Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            
            <!-- Law 1: Value Leap -->
            <div class="fade-in-up delay-1 bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col">
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50">
                    <i class="fa-solid fa-layer-group text-3xl" style="color: rgb(var(--system-blue));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">价值跃迁</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">从“砌墙工”到“总建筑师”</p>
                <div class="mt-6 space-y-4 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    <p><strong class="font-semibold text-red-500">LV1 砌墙工：</strong>按图纸写代码。AI绝对碾压区，结局已定。</p>
                    <p><strong class="font-semibold text-yellow-500">LV2 工程师：</strong>发现并修正错误。人机协作的核心区，你的经验是关键变量。</p>
                    <p><strong class="font-semibold text-green-500">LV3 建筑师：</strong>构想蓝图，判断结构。人类价值的最后堡垒，需要<strong class="text-blue-600 dark:text-blue-400">架构性的想象力</strong>。</p>
                </div>
            </div>

            <!-- Law 2: Skill Reshaping -->
            <div class="fade-in-up delay-2 bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col">
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/50 dark:to-purple-800/50">
                     <i class="fa-solid fa-wand-magic-sparkles text-3xl" style="color: rgb(var(--knowledge-purple));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">技能重塑</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">Prompt是新时代的“通灵术”</p>
                 <p class="mt-6 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    Prompt的本质，不是学几个咒语，而是学习如何将模糊想法翻译成精确指令。这是一种“<strong class="text-purple-600 dark:text-purple-400">逻辑自我剖析</strong>”的能力，是我们与最强“外脑”对话的唯一语言。
                </p>
            </div>

            <!-- Law 3: Cognitive Upgrade -->
            <div class="fade-in-up delay-3 bg-white dark:card-glass shadow-lg rounded-[2rem] p-8 md:p-10 flex flex-col">
                <div class="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/50 dark:to-green-800/50">
                     <i class="fa-solid fa-bullseye text-3xl" style="color: rgb(var(--action-green));"></i>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">认知升级</h2>
                <p class="mt-1 text-md font-semibold text-gray-500 dark:text-gray-400">别做“工具收藏家”，要做“猎人”</p>
                 <p class="mt-6 text-base text-gray-600 dark:text-gray-300 flex-grow">
                    聚焦核心猎场，磨炼核心猎术。你的领域知识，就是你喂给AI的“<strong class="text-green-600 dark:text-green-400">弹药</strong>”。没有弹药，AI这把神枪，在你手里就是烧火棍。
                </p>
            </div>
        </div>
        
        <!-- Ultimate Checklist Section -->
        <section class="mt-24 md:mt-32 fade-in-up">
            <h2 class="text-center text-4xl md:text-5xl font-bold text-gray-800 dark:text-gray-200">你的AI时代生存手册</h2>
            <div class="mt-12 max-w-4xl mx-auto p-8 bg-gray-50 dark:bg-gray-800/50 rounded-[2rem]">
                <dl class="space-y-8">
                    <div>
                        <dt class="text-xl font-semibold text-gray-900 dark:text-white">计算机科学，学不学？</dt>
                        <dd class="mt-2 text-gray-700 dark:text-gray-300">学。但目的不是为了当码农，而是为了安装一个“结构化思考”的操作系统。</dd>
                    </div>
                    <div>
                        <dt class="text-xl font-semibold text-gray-900 dark:text-white">程序员会被替代吗？</dt>
                        <dd class="mt-2 text-gray-700 dark:text-gray-300">会的。只会执行指令的“代码工人”正在被清退。能定义问题的“问题建筑师”，身价将暴涨。</dd>
                    </div>
                    <div>
                        <dt class="text-xl font-semibold text-gray-900 dark:text-white">我们到底该学什么？</dt>
                        <dd class="mt-4 space-y-3">
                            <p class="flex items-center"><i class="fa-solid fa-circle-check mr-3 text-aurora"></i><strong class="font-semibold mr-2">学“通灵术”：</strong>精通Prompt，与AI高效对话。</p>
                            <p class="flex items-center"><i class="fa-solid fa-circle-check mr-3 text-aurora"></i><strong class="font-semibold mr-2">学“跨界”：</strong>建立你独特的领域知识库，那是你的弹药。</p>
                            <p class="flex items-center"><i class="fa-solid fa-circle-check mr-3 text-aurora"></i><strong class="font-semibold mr-2">学“狩猎”：</strong>训练解决未知问题的核心能力，保持好奇。</p>
                        </dd>
                    </div>
                </dl>
            </div>
        </section>


        <!-- Conclusion Section -->
        <section class="text-center mt-24 md:mt-32 fade-in-up">
            <p class="text-lg md:text-xl font-semibold text-gray-600 dark:text-gray-400">黄仁勋的话，不是一个死刑判决书。</p>
            <h2 class="mt-4 text-4xl md:text-6xl font-bold tracking-tight text-gray-800 dark:text-gray-200">
                它是一声集结号，召唤我们开始一场
            </h2>
            <h2 class="mt-2 text-6xl md:text-8xl lg:text-9xl font-black text-aurora">
                认知远征
            </h2>
            <div class="mt-16">
                 <div class="w-20 h-20 mx-auto mb-6 rounded-3xl flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800/50 dark:to-gray-700/50">
                    <i class="fa-solid fa-rocket text-4xl text-gray-700 dark:text-gray-300"></i>
                </div>
                <p class="text-xl md:text-2xl font-bold text-gray-700 dark:text-gray-300">
                    在AI的滔天巨浪面前，持续进化的<strong class="font-black text-gray-900 dark:text-white">学习能力</strong>，<br>是你唯一能紧紧抱住的，<strong class="font-black text-gray-900 dark:text-white">不沉的方舟</strong>。
                </p>
            </div>
        </section>

    </main>
    
    <footer class="fade-in-up delay-4 sticky bottom-4 w-full flex justify-center z-50">
        <div class="mx-auto flex items-center gap-4 rounded-full px-4 py-2 bg-white/50 dark:bg-black/30 backdrop-blur-lg shadow-lg border border-gray-200/50 dark:border-white/10">
            <span class="text-xs text-gray-600 dark:text-gray-400">AI SURVIVAL GUIDE</span>
            <div class="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <button id="theme-toggle" class="w-10 h-10 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-black/5 dark:hover:bg-white/10 transition-colors">
                <i class="fa-solid fa-sun block dark:hidden"></i>
                <i class="fa-solid fa-moon hidden dark:block"></i>
            </button>
        </div>
    </footer>


    <script>
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                }
            });
        }, {
            threshold: 0.1
        });

        document.querySelectorAll('.fade-in-up').forEach(el => {
            observer.observe(el);
        });

        const themeToggle = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;

        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }

        themeToggle.addEventListener('click', () => {
            htmlElement.classList.toggle('dark');
            if (htmlElement.classList.contains('dark')) {
                localStorage.theme = 'dark';
            } else {
                localStorage.theme = 'light';
            }
        });
    </script>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>