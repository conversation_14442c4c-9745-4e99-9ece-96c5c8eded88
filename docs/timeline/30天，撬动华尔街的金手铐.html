<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>30天，撬动华尔街的金手铐</title>
	<script src="https://cdn.tailwindcss.com/3.4.1"></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
	<style>
		@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap');
		body {
			font-family: 'Noto Sans SC', sans-serif;
			background-color: #ffffff;
			color: #000000;
		}
		.gradient-text {
			background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
		}
		.gradient-bg {
			background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
		}
		.fade-in {
			opacity: 0;
			transform: translateY(30px);
			transition: opacity 0.8s ease-out, transform 0.8s ease-out;
		}
		.fade-in.visible {
			opacity: 1;
			transform: translateY(0);
		}
	</style>
</head>
<body class="bg-white">

<main class="overflow-x-hidden">

	<!-- Hero Section -->
	<section class="h-screen flex flex-col items-center justify-center text-center p-8">
		<h1 class="text-5xl md:text-8xl font-black tracking-tight leading-tight fade-in">
			我如何用 <span class="gradient-text">30天</span><br>撬动了华尔街的金手铐
		</h1>
		<p class="mt-8 text-xl md:text-2xl font-bold max-w-3xl fade-in" style="transition-delay: 200ms;">
			“我的领带，曾是一根优雅的绞索。”
			<span class="block text-sm font-normal text-gray-500 mt-2">MY TIE, ONCE AN ELEGANT NOOSE.</span>
		</p>
	</section>

	<!-- The Golden Cage Section -->
	<section class="py-20 md:py-32">
		<div class="max-w-5xl mx-auto px-6 text-center">
			<div class="fade-in">
				<i class="fas fa-building-columns text-5xl gradient-text"></i>
				<p class="mt-6 text-lg md:text-xl leading-relaxed text-gray-700">
					每天清晨，当我系上那条价值不菲的真丝领带，望向纽约黑石集团（BlackRock）47楼窗外的天际线时，我看到的不是机会，而是一个金色的牢笼。我是华尔街的投资组合经理，一个外人看来光鲜亮丽的身份。但我的内心，是一个被“总有一天”病毒感染的囚徒。
				</p>
				<h2 class="mt-12 text-4xl md:text-6xl font-black tracking-tighter">
					我的电脑里，躺着一个<br>名为“<span class="gradient-text">草稿</span>”的坟场。
				</h2>
				<p class="mt-4 text-base text-gray-500">A CEMETERY CALLED "DRAFTS"</p>
			</div>
		</div>
	</section>

	<!-- The Turning Point Section -->
	<section class="py-20 md:py-32 bg-gray-50">
		<div class="max-w-6xl mx-auto px-6">
			<div class="text-center fade-in">
				<h2 class="text-4xl md:text-6xl font-black">
					一个疯狂的念头诞生了。
				</h2>
				<p class="mt-4 text-lg md:text-xl text-gray-600">A RADICAL IDEA WAS BORN</p>
				<p class="mt-8 text-2xl md:text-4xl font-bold max-w-4xl mx-auto">
					我要连续 <span class="gradient-text font-black text-6xl md:text-8xl">30</span> 天，每天在网上发布一篇文章。
				</p>
				<p class="mt-2 text-2xl font-bold tracking-widest text-gray-400">SHIP 30 FOR 30</p>
			</div>

			<div class="mt-20 grid grid-cols-1 md:grid-cols-3 gap-10 text-center">
				<div class="p-8 bg-white rounded-xl shadow-lg fade-in">
					<div class="w-16 h-16 gradient-bg rounded-full mx-auto flex items-center justify-center mb-6">
						<i class="fa-solid fa-check-double text-3xl text-white"></i>
					</div>
					<h3 class="text-2xl font-bold">从“追求完美”到“完成就好”</h3>
					<p class="mt-2 text-gray-600">我没时间雕琢辞藻，只能抓住核心想法，用最快速度写出来。这就是“原子化文章”的雏形。</p>
				</div>
				<div class="p-8 bg-white rounded-xl shadow-lg fade-in" style="transition-delay: 200ms;">
					<div class="w-16 h-16 gradient-bg rounded-full mx-auto flex items-center justify-center mb-6">
						<i class="fa-solid fa-lightbulb text-3xl text-white"></i>
					</div>
					<h3 class="text-2xl font-bold">从“等待灵感”到“主动捕获”</h3>
					<p class="mt-2 text-gray-600">因为第二天就要交稿，我开始像雷达一样扫描生活中的一切，万物皆可是素材。</p>
				</div>
				<div class="p-8 bg-white rounded-xl shadow-lg fade-in" style="transition-delay: 400ms;">
					<div class="w-16 h-16 gradient-bg rounded-full mx-auto flex items-center justify-center mb-6">
						<i class="fa-solid fa-comments text-3xl text-white"></i>
					</div>
					<h3 class="text-2xl font-bold">从“害怕批评”到“拥抱反馈”</h3>
					<p class="mt-2 text-gray-600">我的恐惧，在一次次“发布”按钮的点击声中，被碾得粉碎。</p>
				</div>
			</div>
		</div>
	</section>

	<!-- The Result Section -->
	<section class="py-20 md:py-32">
		<div class="max-w-6xl mx-auto px-6 grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
			<div class="fade-in">
				<p class="text-base font-bold gradient-text">THE TRANSFORMATION</p>
				<h2 class="mt-2 text-4xl md:text-6xl font-black tracking-tighter">
					个人挣扎，变成了<br>集体共鸣。
				</h2>
				<p class="mt-6 text-lg text-gray-700 leading-relaxed">
					第30天，我不仅收获了几百个关注者，更重要的是，我找回了那个敢于表达的自己。我和写作大神 Nicolas Cole 一拍即合，意识到我的个人挣扎，其实是成千上万人的集体困境。
				</p>
				<p class="mt-6 text-lg text-gray-700 leading-relaxed font-bold">
					于是，我们把“Ship 30 for 30”从一个个人挑战，变成了一个价值千万美金的在线教育业务。
				</p>
			</div>
			<div class="fade-in" style="transition-delay: 200ms;">
				<div class="relative p-10 border border-gray-200 rounded-2xl">
					<div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 -z-10 blur-2xl"></div>
					<canvas id="growthChart"></canvas>
				</div>
			</div>
		</div>
	</section>

	<!-- Three Truths Section -->
	<section class="py-20 md:py-32 bg-gray-900 text-white">
		<div class="max-w-6xl mx-auto px-6">
			<div class="text-center mb-20 fade-in">
				<h2 class="text-4xl md:text-6xl font-black">三个反直觉的真相</h2>
				<p class="mt-4 text-lg text-gray-400">THREE COUNTER-INTUITIVE TRUTHS</p>
			</div>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
				<div class="border border-gray-700 p-8 rounded-2xl fade-in">
					<div class="text-5xl mb-6 gradient-text"><i class="fas fa-rocket"></i></div>
					<h3 class="text-2xl font-bold mb-3">约束是自由的引擎，不是刹车。</h3>
					<p class="text-gray-400">给自己一个极小的、不容置疑的约束，它会像火箭推进器一样，把你送上轨道。</p>
				</div>
				<div class="border border-gray-700 p-8 rounded-2xl fade-in" style="transition-delay: 200ms;">
					<div class="text-5xl mb-6 gradient-text"><i class="fas fa-gem"></i></div>
					<h3 class="text-2xl font-bold mb-3">你的“问题”，就是你的“产品”。</h3>
					<p class="text-gray-400">你最真实的挣扎，就是你最有价值的宝藏，因为背后有无数个“过去的你”在等待答案。</p>
				</div>
				<div class="border border-gray-700 p-8 rounded-2xl fade-in" style="transition-delay: 400ms;">
					<div class="text-5xl mb-6 gradient-text"><i class="fas fa-dumbbell"></i></div>
					<h3 class="text-2xl font-bold mb-3">互联网不是舞台，而是你的健身房。</h3>
					<p class="text-gray-400">你的每一次“发布”，都是一次力量训练。数量会孕育出质量，坚持会带来奇迹。</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Final CTA Section -->
	<section class="py-24 md:py-48">
		<div class="max-w-4xl mx-auto px-6 text-center fade-in">
			<h2 class="text-3xl md:text-5xl font-bold leading-tight">
				世界不需要你那本锁在抽屉里的完美小说。
				<br>
				<span class="mt-4 block gradient-text">世界需要的，是你今天那个粗糙、但真诚的想法。</span>
			</h2>
			<div class="mt-16">
				<h3 class="text-5xl md:text-7xl font-black">现在，轮到你了。</h3>
				<p class="mt-8 text-3xl md:text-5xl font-bold text-gray-800">
					你，今天打算“<span class="underline decoration-wavy decoration-blue-500">发布</span>”点什么？
				</p>
			</div>
		</div>
	</section>

</main>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
	// Fade-in animation on scroll
	const faders = document.querySelectorAll('.fade-in');
	const appearOptions = {
		threshold: 0.2,
		rootMargin: "0px 0px -50px 0px"
	};
	const appearOnScroll = new IntersectionObserver(function(entries, appearOnScroll) {
		entries.forEach(entry => {
			if (!entry.isIntersecting) {
				return;
			} else {
				entry.target.classList.add('visible');
				appearOnScroll.unobserve(entry.target);
			}
		});
	}, appearOptions);
	faders.forEach(fader => {
		appearOnScroll.observe(fader);
	});

	// Chart.js data and config
	const ctx = document.getElementById('growthChart').getContext('2d');
	const gradient = ctx.createLinearGradient(0, 0, 0, 400);
	gradient.addColorStop(0, 'rgba(59, 130, 246, 0.5)');
	gradient.addColorStop(1, 'rgba(139, 92, 246, 0)');

	new Chart(ctx, {
		type: 'line',
		data: {
			labels: ['Day 1', 'Day 10', 'Day 20', 'Day 30', 'Launch'],
			datasets: [{
				label: 'Momentum Growth',
				data: [5, 25, 60, 100, 400],
				backgroundColor: gradient,
				borderColor: '#6366f1',
				borderWidth: 3,
				pointBackgroundColor: '#ffffff',
				pointBorderColor: '#6366f1',
				pointBorderWidth: 2,
				pointRadius: 5,
				fill: true,
				tension: 0.4
			}]
		},
		options: {
			responsive: true,
			plugins: {
				legend: {
					display: false
				},
				tooltip: {
					backgroundColor: '#000',
					titleFont: { size: 14, weight: 'bold' },
					bodyFont: { size: 12 },
					padding: 12,
					cornerRadius: 8
				}
			},
			scales: {
				y: {
					beginAtZero: true,
					grid: {
						color: 'rgba(200, 200, 200, 0.1)'
					},
					ticks: {
						color: '#6b7280'
					}
				},
				x: {
					grid: {
						display: false
					},
					ticks: {
						color: '#6b7280'
					}
				}
			}
		}
	});
</script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>