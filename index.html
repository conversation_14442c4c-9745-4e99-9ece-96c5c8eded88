<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字海洋的月光</title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/homepage.css">
</head>
<body class="selection:bg-[#689094]/30 selection:text-[#3D3D3D]">
    <!-- 添加导航栏 -->
    <header class="w-full bg-white shadow-md fixed top-0 left-0 z-10" data-component="header"></header>

    <!-- 添加"鲸波月影"部分 -->
    <div id="whale-moon-title" class="w-full bg-[var(--color-background)] text-center py-16 mt-16">
        <h1 class="whale-moon-text text-6xl md:text-8xl text-gray-800">
            <span class="char" data-char="鲸">鲸</span>
            <span class="char" data-char="波">波</span>
            <span class="char" data-char="月">月</span>
            <span class="char" data-char="影">影</span>
        </h1>
    </div>

    <div id="hero-content" class="content-wrapper container">
        <div class="binary-bg" aria-hidden="true">
            <span>0</span><span>1</span><span>1</span><span>0</span><span>1</span><span>0</span><span>0</span><span>1</span>
            <span>1</span><span>0</span><span>0</span><span>1</span><span>0</span><span>1</span><span>1</span><span>0</span>
            <span>0</span><span>1</span><span>1</span><span>0</span><span>1</span><span>0</span><span>0</span><span>1</span>
        </div>

        <div class="mb-4 md:mb-6">
            <p class="text-xl md:text-3xl font-context text-secondary ocean-waves">
                在 <span class="text-2xl md:text-4xl text-tertiary wave-number">0</span> 与 <span class="text-2xl md:text-4xl text-tertiary wave-number">1</span> 的海洋
            </p>
        </div>

        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-display">
            <span class="water-ripple">打捞</span>
            <br class="sm:hidden"> <!-- Break line on very small screens -->
            <span class="block mt-2 md:mt-3">
                <span class="text-highlight-gradient moonlight-fragments" data-text="月光">月光</span><span class="text-highlight-gradient moonlight-fragments" data-text="碎片">碎片</span>
            </span>
        </h1>

        <p class="text-sm md:text-md uppercase tracking-wider mt-8 md:mt-12 text-tertiary">
            SALVAGING MOONLIGHT IN A DIGITAL SEA
        </p>
    </div>

    <!-- 分页时的顶部间距占位 -->
    <div id="pagination-spacer" class="hidden" style="height: 120px; margin-top: 64px;"></div>

    <!-- 置顶文章预览组件 -->
    <div data-component="pinned-previews" id="pinned-previews"></div>

    <!-- 普通文章预览组件 -->
    <div data-component="iframe-previews" id="iframe-previews"></div>

    <!-- 显示更多按钮和分页控制 -->
    <div id="article-controls" class="text-center mt-12 mb-16" style="display: none;">
        <button id="show-all-btn" class="show-all-button">
            <span class="text-highlight-gradient font-context">显示所有文章</span>
        </button>
        <div id="pagination-controls" class="mt-8" style="display: none;">
            <div class="pagination-wrapper">
                <button id="first-page" class="pagination-btn pagination-btn-disabled" disabled title="第一页">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prev-page" class="pagination-btn pagination-btn-disabled" disabled title="上一页">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="page-info">
                    <span id="page-info" class="text-secondary font-context">第 1 页，共 1 页</span>
                </div>
                <button id="next-page" class="pagination-btn pagination-btn-disabled" disabled title="下一页">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button id="last-page" class="pagination-btn pagination-btn-disabled" disabled title="最后一页">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>









    <div class="mt-16">
        <p class="text-sm text-tertiary moonlight-shimmer">鲸波深处，代码与月光共振</p>
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-12" data-component="footer"></footer>
    <!-- 加载组件脚本 -->
    <script src="js/main.js"></script>
</body>
</html>
