<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后代码时代：Cursor 联合创始人 Michael 访谈精粹</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="../../css/styles.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Roboto:wght@300;400;700&display=swap');
        body {
            font-family: 'Noto Sans SC', 'Roboto', sans-serif;
            background-color: #FFFFFF;
            color: #000000;
        }
        .highlight-gradient-text {
            background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .highlight-gradient-bg {
            background-image: linear-gradient(to right, rgba(59, 130, 246, 0.7), rgba(139, 92, 246, 0.7), rgba(236, 72, 153, 0.7));
        }
        .highlight-gradient-border {
             background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
        }
        .content-section h2 {
            font-size: 2.5rem; /* 40px */
            font-weight: 900;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        .content-section h2 .icon {
            font-size: 2rem;
            margin-right: 0.75rem;
            color: #8b5cf6; /* Purple accent */
        }
        .content-section h3 {
            font-size: 1.875rem; /* 30px */
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #3b82f6; /* Blue accent */
        }
        .content-section p, .content-section ul li {
            font-size: 1.125rem; /* 18px */
            line-height: 1.8;
            margin-bottom: 1rem;
            color: #333333;
        }
        .content-section strong {
            font-weight: 700;
            color: #000000;
        }
        .quote {
            font-size: 1.5rem; /* 24px */
            font-weight: 700;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 5px solid;
            border-image-slice: 1;
            border-image-source: linear-gradient(to bottom, #3b82f6, #8b5cf6, #ec4899);
            background-color: rgba(243, 244, 246, 0.5); /* Light gray bg */
        }
        .quote .english-sub {
            display: block;
            font-size: 1rem;
            font-weight: 400;
            color: #6b7280; /* Gray-500 */
            margin-top: 0.25rem;
        }
        .large-visual-text {
            font-size: 4rem; /* 64px */
            font-weight: 900;
            text-align: center;
            margin: 3rem 0;
        }
        @media (min-width: 768px) {
            .large-visual-text {
                font-size: 6rem; /* 96px */
            }
            .content-section h2 {
                font-size: 3rem; /* 48px */
            }
        }
        .tech-line-art {
            width: 100%;
            max-width: 300px;
            height: auto;
            margin: 1.5rem auto;
            opacity: 0.7;
            border: 2px solid transparent; /* Initial transparent border */
            border-image-slice: 1;
            border-image-source: linear-gradient(to right, rgba(59,130,246,0.5), rgba(139,92,246,0.5), rgba(236,72,153,0.5));
            padding: 10px;
            box-sizing: border-box;
        }
        .tech-line-art path {
            stroke: url(#lineArtGradient); /* Apply gradient to stroke */
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body class="antialiased">
    <svg width="0" height="0" style="position:absolute">
      <defs>
        <linearGradient id="lineArtGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16 max-w-screen-xl">

        <!-- Header Section -->
        <header class="text-center mb-12 md:mb-20">
            <h1 class="text-5xl md:text-7xl font-black mb-4">
                后代码时代 <span class="block text-3xl md:text-4xl text-gray-500 font-normal mt-2">THE POST-CODE ERA</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto">
                Cursor 联合创始人 <strong class="highlight-gradient-text">Michael</strong> 深度访谈：探索软件构建的未来范式。
            </p>
            <div class="mt-6 h-1.5 w-32 mx-auto highlight-gradient-border"></div>
        </header>

        <!-- Introduction -->
        <section class="content-section mb-12 md:mb-16">
            <h2><span class="icon"><i class="fas fa-podcast"></i></span>访谈引言 <span class="text-lg text-gray-400 ml-2 font-light">INTRODUCTION</span></h2>
            <p>Cursor 大家都知道，但是作为Cursor联合创始人的Michael，却很少露面。这次他参加了一个访谈，从这样的访谈中，也可以更多的了解Cursor本身，如果你正在使用Cursor的话。</p>
            <p>Cursor其实想要提供的是，一种构建软件的<strong class="text-blue-600">简化方式</strong>，可以简化到你只需要描述你的软件长什么样，能做什么即可。这其实已经是我们在使用Cursor构建软件的方式，但是当我敲出这个定义时，我还是本能的感觉到很不一样，但又显得那么的自然。</p>
            <svg class="tech-line-art" viewBox="0 0 100 50" xmlns="http://www.w3.org/2000/svg">
                <path d="M 5 25 Q 25 5, 45 25 T 85 25" />
                <text x="40" y="40" font-family="Roboto" font-size="5" fill="#8b5cf6">Evolution of Coding</text>
            </svg>
        </section>

        <div class="large-visual-text highlight-gradient-text">
            逻辑工程师
            <span class="block text-2xl md:text-4xl text-black font-bold mt-2">LOGIC ENGINEERS</span>
        </div>

        <!-- Core Themes -->
        <section class="content-section mb-12 md:mb-16">
            <h2><span class="icon"><i class="fas fa-cogs"></i></span>核心洞察 <span class="text-lg text-gray-400 ml-2 font-light">KEY INSIGHTS</span></h2>

            <h3><i class="fas fa-code-branch text-purple-500 mr-2"></i>从高级语言到自然语言的飞跃</h3>
            <p>毕竟我们构建软件是一路从机器语言，不断的抽象到了过程式语言，再到对象等高级编程语言，最后还是来到了<strong class="text-purple-600">自然语言</strong>。所以访谈中Michael 谈到的是，现在构建软件的方式分为两部分：一部分是<strong class="text-pink-500">视觉</strong>，用户可见的部分，这部分反而有很多方式可以进行指定的展示；另一部分是背后的<strong class="text-blue-600">逻辑</strong>，这也是Michael 一直在说，软件工程师其实已经变成了<strong class="text-orange-500">逻辑工程师</strong>的缘故。我们构建软件，其实是在构建逻辑。</p>

            <h3><i class="fas fa-lightbulb text-purple-500 mr-2"></i>Vibe Coding 的局限与精准控制</h3>
            <p>对于现在使用文本代码编辑器+高级编程语言的工程师来说，我们其实开始写伪代码了。但这个又跟vibe coding有一定的区别，vibe coding的最大挑战就是，当Cursor 帮你生成大量的代码后，你失去了对于整个细节的控制，生成代码太多太快了。到了最后你会发现，哪怕是一个很简单的问题，Cursor也会来来回回，怎么也修复不了。原因就是<strong class="text-blue-600">缺少对于细节的控制</strong>，如果你发现了问题所在，其实使用Cursor一下子就修复掉了。</p>

            <div class="my-8 p-6 rounded-lg highlight-gradient-bg text-white shadow-xl">
                <p class="text-2xl font-bold mb-2"><i class="fas fa-terminal mr-2"></i>Cursor 的理想境界：</p>
                <p class="text-lg">大家开始不要用typescript，java等编程语言的方式去思考，他们看不到代码了，或者他们不关心是什么类型的代码了，他们只关心逻辑。我们用<strong class="text-yellow-300">英语写大量的伪代码</strong>，这可以准确的表达我们的意图。</p>
            </div>
            <p>现在我明白了为什么一些好用的prompt都是大段大段的写了，这可能才是正确使用LLM的方式，而Cursor 作为一个AI工具，它可能添加了更多的上下文和提示词。</p>
            <svg class="tech-line-art" viewBox="0 0 100 60" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="5" width="80" height="15" rx="2" ry="2" fill="rgba(59,130,246,0.1)" />
                <text x="15" y="15" font-family="Roboto" font-size="4" fill="#3b82f6">Pseudocode: Describe logic clearly</text>
                <path d="M 50 22 V 32" stroke-dasharray="2 2"/>
                <text x="52" y="28" font-family="Roboto" font-size="4" fill="#8b5cf6"><i class="fas fa-arrow-down"></i> AI Generates</text>
                <rect x="10" y="35" width="80" height="20" rx="2" ry="2" fill="rgba(139,92,246,0.1)" />
                <text x="15" y="42" font-family="Roboto" font-size="3" fill="#8b5cf6">// Generated Code Block</text>
                <text x="15" y="48" font-family="Roboto" font-size="3" fill="#8b5cf6">function example() { ... }</text>
            </svg>
        </section>

        <div class="quote">
            “关注于‘要构建什么’而不是‘如何构建它’。”
            <span class="english-sub">"Focus on 'WHAT TO BUILD' rather than 'HOW TO BUILD IT'."</span>
        </div>

        <section class="content-section mb-12 md:mb-16">
            <h2><span class="icon"><i class="fas fa-palette"></i></span>品味与实现 <span class="text-lg text-gray-400 ml-2 font-light">TASTE & EXECUTION</span></h2>
            <p>而Michael一直强调的是，对于代码的<strong class="text-purple-600">口味（taste）</strong>。有了Cursor 这类工具，应该关注于“要构建什么”而不是“如何构建它”。这个挺有意思的，跟我们之前专注的重点开始有了偏差。之前我们使用各种编程语言时，我们更加专注于怎么高效、优雅的实现它。</p>
            <p>当说起品味，我突然发现，有时候有些问题 Cursor 一直改不掉，进入死机状态，很有可能不是它的问题，而是<strong class="text-orange-500">我的品味问题</strong>。因为之前我自己用 Cursor 做了一个笔记应用，关于文本框、图片显示区域、取消提交按钮，三者的相对位置，怎么调整都不对，使得写作体验很差。后面看到这个访谈中说的品味，我突然觉得，可能是我错了。于是我重新调整了布局，比如按照 flomo 或者语雀的布局。突然间 Cursor 又能很正常的工作了。</p>
        </section>

        <div class="large-visual-text highlight-gradient-text">
            我们是 PLANNER
            <span class="block text-2xl md:text-4xl text-black font-bold mt-2">WE ARE PLANNERS</span>
        </div>
        <p class="text-center text-2xl md:text-3xl font-bold -mt-8 mb-12 md:mb-20">
            Cursor 是 EXECUTOR <span class="text-lg text-gray-500 ml-2">Cursor is the EXECUTOR</span>
        </p>


        <section class="content-section mb-12 md:mb-16">
             <h2><span class="icon"><i class="fas fa-users-cog"></i></span>工程师的新角色 <span class="text-lg text-gray-400 ml-2 font-light">THE NEW ROLE OF ENGINEERS</span></h2>
            <p>而现在我们都变成了<strong class="text-blue-600">指挥者</strong>，我们是<strong class="text-purple-600">planner</strong>，Cursor才是<strong class="text-pink-500">执行者</strong>，把实现留给它，它写的比大多数人要好，将来会更好。</p>
            <svg class="tech-line-art" viewBox="0 0 100 50" xmlns="http://www.w3.org/2000/svg">
                <circle cx="25" cy="25" r="8" />
                <text x="20" y="27" font-family="FontAwesome" font-size="7" fill="url(#lineArtGradient)"></text> <!-- User icon -->
                <text x="18" y="38" font-family="Roboto" font-size="4" fill="#3b82f6">Planner</text>
                <path d="M 35 25 H 55 L 50 20 M 55 25 L 50 30" />
                <circle cx="75" cy="25" r="8" />
                <text x="70" y="27" font-family="FontAwesome" font-size="7" fill="url(#lineArtGradient)"></text> <!-- Code icon / AI -->
                <text x="68" y="38" font-family="Roboto" font-size="4" fill="#8b5cf6">Executor</text>
            </svg>
        </section>

        <section class="content-section">
            <h2><span class="icon"><i class="fas fa-chart-line"></i></span>未来展望 <span class="text-lg text-gray-400 ml-2 font-light">FUTURE OUTLOOK</span></h2>
            <p>最后Michael 还说了，软件的需求是<strong class="text-green-500">巨大的</strong>（估计已经包括了各种长尾需求和个人各类工具的需求），但是确实是的，如果构建软件的成本下降了，需求其实会冒出来更多，比如企业的各种定制化需求。从这点看，对于工程师的需求会更多，但工程师还是要<strong class="text-orange-500">转型</strong>，学会使用AI coding工具来构建软件。</p>
             <div class="mt-8 p-6 border-2 border-dashed rounded-lg" style="border-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899) 1;">
                <p class="text-xl font-bold text-center">
                    <i class="fas fa-rocket mr-2 highlight-gradient-text"></i>
                    拥抱变革，创造未来。
                    <span class="block text-sm text-gray-600 mt-1">Embrace change, create the future.</span>
                </p>
            </div>
        </section>

        <footer class="text-center mt-16 md:mt-24 py-8 border-t border-gray-200">
            <p class="text-gray-600">© <script>document.write(new Date().getFullYear())</script> 后代码时代访谈精粹。 AI辅助生成演示页面。</p>
            <p class="text-sm text-gray-500 mt-1">Generated by Dynamic Web Page Assistant.</p>
        </footer>

    </div>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>