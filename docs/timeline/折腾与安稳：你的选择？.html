<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折腾与安稳：你的选择？</title>
    <script src="https://cdn.tailwindcss.com/3.4.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #050507;
            color: #E5E7EB;
            overflow-x: hidden;
        }

        .hybrid-grid-card {
            background: rgba(255, 255, 255, 0.03);
            -webkit-backdrop-filter: blur(50px);
            backdrop-filter: blur(50px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .hybrid-grid-card:hover {
            background: rgba(255, 255, 255, 0.07);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 动态背景光斑 */
        .blur-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(160px);
            opacity: 0.4;
            pointer-events: none;
            transition: all 2s ease;
        }

        .blob-1 {
            width: 700px;
            height: 700px;
            background: #BF40BF; /* Vibrant Magenta */
            top: -20%;
            left: -30%;
            animation: move-blob-1 40s infinite alternate ease-in-out;
        }

        .blob-2 {
            width: 600px;
            height: 600px;
            background: #0071E3; /* Cool Blue */
            bottom: -25%;
            right: -30%;
            animation: move-blob-2 35s infinite alternate ease-in-out;
        }

        @keyframes move-blob-1 {
            from { transform: translate(0, 0) scale(1) rotate(0deg); }
            to { transform: translate(150px, 100px) scale(1.1) rotate(45deg); }
        }
        @keyframes move-blob-2 {
            from { transform: translate(0, 0) scale(1) rotate(0deg); }
            to { transform: translate(-100px, -150px) scale(1.2) rotate(-60deg); }
        }

        /* 鼠标跟随光晕 */
        #cursor-light {
            position: fixed;
            width: 800px;
            height: 800px;
            left: 0;
            top: 0;
            transform: translate(-50%, -50%);
            background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 0;
        }

        /* 入场动画 */
        .reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1s cubic-bezier(0.215, 0.610, 0.355, 1), transform 1s cubic-bezier(0.215, 0.610, 0.355, 1);
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 图标微动画 */
        .heart-tired-anim {
            animation: pulse-slow 3s infinite ease-in-out;
        }
        @keyframes pulse-slow {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.05); opacity: 1; }
        }
        .heart-broken-anim {
            animation: shake-subtle 5s infinite ease-in-out;
        }
        @keyframes shake-subtle {
            0%, 100% { transform: rotate(-1deg); }
            50% { transform: rotate(1deg); }
        }
    </style>
</head>
<body class="antialiased">

    <!-- 动态背景与光晕 -->
    <div class="fixed inset-0 z-[-1] overflow-hidden">
        <div class="blur-blob blob-1"></div>
        <div class="blur-blob blob-2"></div>
    </div>
    <div id="cursor-light"></div>

    <div class="relative min-h-screen w-full py-20 px-4 sm:px-6 lg:px-8 z-10">
        <main class="max-w-7xl mx-auto flex flex-col gap-24 md:gap-32">

            <!-- Hero Section: The Choice -->
            <section class="min-h-[60vh] flex flex-col md:flex-row items-center justify-center gap-8 md:gap-16 reveal">
                <div class="text-center md:text-right w-full md:w-1/2">
                    <i class="fa-solid fa-fire text-5xl text-fuchsia-400 mb-4"></i>
                    <h1 class="text-5xl sm:text-6xl lg:text-7xl font-black text-white leading-tight">
                        若无一颗<br>折腾到死的心
                    </h1>
                    <p class="text-sm uppercase tracking-widest text-fuchsia-400/80 mt-2">The Relentless Heart</p>
                </div>
                <div class="text-center md:text-left w-full md:w-1/2">
                    <i class="fa-solid fa-shield-heart text-5xl text-blue-400 mb-4"></i>
                    <h1 class="text-5xl sm:text-6xl lg:text-7xl font-black text-white leading-tight">
                        就守好一份<br>安稳度日的薪
                    </h1>
                     <p class="text-sm uppercase tracking-widest text-blue-400/80 mt-2">The Sheltered Salary</p>
                </div>
            </section>

            <!-- The Consequence: Tired vs Broken -->
            <section class="reveal">
                <div class="text-center mb-12">
                    <h2 class="text-2xl font-bold text-gray-400">毕竟...</h2>
                </div>
                <div class="grid md:grid-cols-2 gap-8 items-stretch">
                    <!-- The Job -->
                    <div class="hybrid-grid-card rounded-3xl p-8 lg:p-12 flex flex-col text-center items-center justify-between border-blue-500/30">
                        <div class="w-full">
                            <p class="text-lg uppercase tracking-widest text-blue-400">上班</p>
                            <p class="text-sm text-gray-400">The 9-to-5</p>
                        </div>
                        <div class="my-12">
                            <i class="fa-solid fa-heart text-8xl lg:text-9xl text-blue-300 heart-tired-anim"></i>
                        </div>
                        <h3 class="text-8xl lg:text-9xl font-black text-white">心累</h3>
                        <p class="text-lg text-gray-400 mt-2">Mentally Tired</p>
                    </div>

                    <!-- The Venture -->
                    <div class="hybrid-grid-card rounded-3xl p-8 lg:p-12 flex flex-col text-center items-center justify-between border-fuchsia-500/30">
                         <div class="w-full">
                            <p class="text-lg uppercase tracking-widest text-fuchsia-400">创业</p>
                            <p class="text-sm text-gray-400">The Venture</p>
                        </div>
                        <div class="my-12">
                            <i class="fa-solid fa-heart-crack text-8xl lg:text-9xl text-fuchsia-400 heart-broken-anim"></i>
                        </div>
                        <h3 class="text-8xl lg:text-9xl font-black text-white">心碎</h3>
                        <p class="text-lg text-gray-400 mt-2">Potentially Broken</p>
                    </div>
                </div>
            </section>

            <!-- Final Reflection -->
            <section class="text-center py-16 reveal">
                 <div class="hybrid-grid-card rounded-3xl p-10 md:p-16 max-w-3xl mx-auto">
                    <i class="fa-solid fa-scale-balanced text-5xl text-gray-300 mb-8"></i>
                    <h2 class="text-4xl sm:text-5xl md:text-6xl font-bold text-white">
                        你的选择?
                    </h2>
                    <p class="text-sm uppercase tracking-widest text-gray-400 mt-4">Choose Your Path</p>
                 </div>
            </section>

        </main>
    </div>

    <script>
        // 鼠标跟随光晕效果
        const cursorLight = document.getElementById('cursor-light');
        document.addEventListener('mousemove', (e) => {
            cursorLight.style.left = e.clientX + 'px';
            cursorLight.style.top = e.clientY + 'px';
        });

        // 滚动入场动画
        const revealElements = document.querySelectorAll('.reveal');
        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, {
            threshold: 0.1
        });

        revealElements.forEach(el => {
            revealObserver.observe(el);
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>