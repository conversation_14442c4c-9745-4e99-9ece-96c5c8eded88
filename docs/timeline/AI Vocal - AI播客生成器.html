<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Vocal - AI播客生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="../../css/styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #ffffff;
            color: #000000;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }
        .gradient-text {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
        .highlight-gradient-main {
            background-image: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899); /* Blue to Purple to Pink/Orange-red */
        }
        .highlight-gradient-blue-purple {
             background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
        }
        .highlight-gradient-purple-pink {
             background-image: linear-gradient(to right, #8b5cf6, #ec4899);
        }
        .highlight-gradient-pink-orange {
            background-image: linear-gradient(to right, #ec4899, #f97316);
        }
        .btn-primary {
            background-image: linear-gradient(to right, #2563eb, #7c3aed);
            color: white;
            padding: 0.875rem 2rem; /* py-3.5 px-8 */
            border-radius: 0.5rem; /* rounded-lg */
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .btn-primary:hover {
            background-image: linear-gradient(to right, #1d4ed8, #6d28d9);
            box-shadow: 0 10px 20px -5px rgba(59, 130, 246, 0.4);
        }
        .icon-outline {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 72px; /* w-18 */
            height: 72px; /* h-18 */
            border-radius: 0.75rem; /* rounded-xl */
            border: 3px solid transparent;
            background-clip: padding-box, border-box;
            background-origin: padding-box, border-box;
            position: relative;
        }
        .icon-outline i {
            font-size: 2rem; /* text-3xl */
        }
        .icon-outline.border-gradient-blue-purple {
            border-image: linear-gradient(to bottom right, #3b82f6, #8b5cf6) 1;
        }
        .icon-outline.border-gradient-purple-pink {
            border-image: linear-gradient(to bottom right, #8b5cf6, #ec4899) 1;
        }
         .icon-outline.border-gradient-pink-orange {
            border-image: linear-gradient(to bottom right, #ec4899, #f97316) 1;
        }

        .tech-line-decoration {
            height: 5px;
            width: 80px;
            margin-top: 1rem; /* mt-4 */
            margin-bottom: 1.5rem; /* mb-6 */
            opacity: 0.7;
        }
        .tag {
            background-color: rgba(0,0,0,0.07);
            color: #000;
            padding: 0.3rem 0.9rem;
            border-radius: 9999px;
            font-size: 0.8rem;
            margin-right: 0.5rem;
            display: inline-block;
            font-weight: 500;
        }
        .author-date-tags {
            margin-top: 1rem;
            margin-bottom: 2rem;
            font-size: 0.9rem;
            color: #374151; /* text-gray-700 */
        }
        .bg-opacity-gradient-blue {
            background: linear-gradient(to bottom, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0));
        }
        .bg-opacity-gradient-purple {
            background: linear-gradient(to bottom, rgba(139, 92, 246, 0.2), rgba(139, 92, 246, 0));
        }
        .shadow-hero {
            text-shadow: 0px 0px 30px rgba(59, 130, 246, 0.3), 0px 0px 50px rgba(139, 92, 246, 0.2);
        }
        .accordion-item {
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .accordion-button {
            width: 100%;
            text-align: left;
            padding: 1.5rem 1rem;
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
            background-color: white;
            border: none;
            position: relative;
            cursor: pointer;
        }
        .accordion-button::after {
            content: '\f078'; /* Font Awesome chevron-down */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }
        .accordion-button.open::after {
            transform: translateY(-50%) rotate(180deg);
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-out;
            padding: 0 1rem;
            background-color: #f9fafb; /* bg-gray-50 */
        }
        .accordion-content p {
            padding: 1.5rem 0;
            color: #374151; /* text-gray-700 */
        }
        .large-bg-text {
            font-size: 12rem; /* Adjust as needed */
            font-weight: 800; /* font-extrabold or black */
            position: absolute;
            z-index: 0;
            opacity: 0.05;
            pointer-events: none;
            user-select: none;
        }
    </style>
</head>
<body class="antialiased">

    <div class="relative min-h-screen px-4 py-8 sm:px-6 lg:px-8">

        <!-- Hero Section -->
        <section class="text-center pt-12 pb-16 md:pt-20 md:pb-24 relative">
            <div class="absolute top-1/4 left-10 large-bg-text highlight-gradient-main gradient-text opacity-5">AI</div>
            <div class="absolute top-1/2 right-10 large-bg-text highlight-gradient-main gradient-text opacity-5 transform -rotate-6">PODCAST</div>
            <div class="relative z-10">
                <h1 class="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-4 shadow-hero">
                    <span class="highlight-gradient-main gradient-text">AI VOCAL</span> 播客生成器
                </h1>
                <p class="text-2xl sm:text-3xl md:text-4xl font-bold max-w-4xl mx-auto mb-3">
                    告别繁琐录制，用AI将<span class="highlight-gradient-blue-purple gradient-text">文字笔记</span><br class="hidden md:inline"/>
                    数分钟化为<span class="highlight-gradient-purple-pink gradient-text">录音室级播客</span>
                </p>
                <p class="text-lg md:text-xl text-gray-600 mb-8">
                    Instantly Convert Text to Studio-Quality Podcasts.
                </p>
                <div class="author-date-tags text-center">
                    <span class="font-semibold">JerryBob</span> | <span id="current-date"></span>
                    <div class="mt-3">
                        <span class="tag">工作</span>
                        <span class="tag">AI工具</span>
                        <span class="tag">播客创作</span>
                    </div>
                </div>
                <a href="https://aivocal.io/ai-podcast" class="btn-primary text-xl">免费试用AI播客</a>
            </div>
        </section>

        <!-- Problem & Solution Intro Section -->
        <section class="py-16 md:py-24 text-center relative">
            <div class="max-w-5xl mx-auto">
                <div class="grid md:grid-cols-2 gap-8 items-center">
                    <div class="text-left">
                         <h2 class="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6">创作播客，<br/>从未如此<span class="highlight-gradient-main gradient-text">简单高效</span></h2>
                        <p class="text-lg text-gray-700 mb-4">
                            传统播客制作耗时费力，技术门槛高？<br/>
                            <span class="font-bold">AI Vocal</span> 彻底改变播客创作流程，让您的声音轻松传播。
                        </p>
                        <p class="text-sm text-gray-500">Traditional podcasting is time-consuming and requires technical skills. AI Vocal revolutionizes the workflow.</p>
                    </div>
                    <div class="flex justify-center items-center">
                        <div class="p-8 rounded-full bg-opacity-gradient-blue w-64 h-64 md:w-80 md:h-80 flex flex-col justify-center items-center">
                            <span class="text-7xl md:text-8xl font-extrabold highlight-gradient-blue-purple gradient-text">90%</span>
                            <p class="text-xl font-semibold mt-2">创作者渴望</p>
                            <p class="text-md text-gray-700">更高效的音频内容生产</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 md:py-24 bg-gray-50 rounded-xl">
            <div class="max-w-6xl mx-auto px-4">
                <h2 class="text-4xl sm:text-5xl font-extrabold text-center mb-16">AI Vocal <span class="highlight-gradient-main gradient-text">核心优势</span></h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="feature-item p-6 text-center">
                        <div class="icon-outline border-gradient-blue-purple mx-auto mb-6"><i class="fas fa-brain highlight-gradient-blue-purple gradient-text"></i></div>
                        <h3 class="text-2xl font-bold mb-2">智能驱动，告别麦克风</h3>
                        <p class="text-sm text-gray-600 mb-2">AI-POWERED CONTENT CREATION</p>
                        <p class="text-gray-700">强大AI引擎，直接将文本笔记、文章或脚本转化为自然流畅播客音频，无需任何录音设备。</p>
                        <div class="tech-line-decoration highlight-gradient-blue-purple mx-auto opacity-50"></div>
                    </div>
                    <div class="feature-item p-6 text-center">
                        <div class="icon-outline border-gradient-purple-pink mx-auto mb-6"><i class="fas fa-voicemail highlight-gradient-purple-pink gradient-text"></i></div>
                        <h3 class="text-2xl font-bold mb-2">真人级AI语音库</h3>
                        <p class="text-sm text-gray-600 mb-2">REALISTIC AI VOICES</p>
                        <p class="text-gray-700">多种超逼真AI语音、语调、语速随心选，完美匹配您的内容风格与品牌形象。</p>
                        <div class="tech-line-decoration highlight-gradient-purple-pink mx-auto opacity-50"></div>
                    </div>
                    <div class="feature-item p-6 text-center">
                        <div class="icon-outline border-gradient-pink-orange mx-auto mb-6"><i class="fas fa-tasks highlight-gradient-pink-orange gradient-text"></i></div>
                        <h3 class="text-2xl font-bold mb-2">三步极简操作</h3>
                        <p class="text-sm text-gray-600 mb-2">EFFORTLESS 3-STEP PROCESS</p>
                        <p class="text-gray-700">输入文本 > 选择语音 > 一键生成。像编辑文档一样简单，新手也能快速上手。</p>
                        <div class="tech-line-decoration highlight-gradient-pink-orange mx-auto opacity-50"></div>
                    </div>
                    <div class="feature-item p-6 text-center">
                        <div class="icon-outline border-gradient-blue-purple mx-auto mb-6"><i class="fas fa-bolt highlight-gradient-blue-purple gradient-text"></i></div>
                        <h3 class="text-2xl font-bold mb-2">完全免费，即刻体验</h3>
                        <p class="text-sm text-gray-600 mb-2">FREE & INSTANT</p>
                        <p class="text-gray-700">无需付费，无需安装。在线工具，随时随地将您的灵感转化为动听播客。</p>
                        <div class="tech-line-decoration highlight-gradient-blue-purple mx-auto opacity-50"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How it Works Section -->
        <section class="py-16 md:py-24 text-center">
            <div class="max-w-5xl mx-auto px-4">
                <h2 class="text-6xl sm:text-7xl font-extrabold mb-4"><span class="highlight-gradient-main gradient-text">三步</span></h2>
                <p class="text-3xl sm:text-4xl font-bold mb-16">轻松生成专业播客</p>
                <div class="grid md:grid-cols-3 gap-8 md:gap-12">
                    <div class="step-item p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                        <div class="text-7xl font-extrabold highlight-gradient-blue-purple gradient-text mb-4">01</div>
                        <i class="fas fa-paste text-5xl mb-4 highlight-gradient-blue-purple gradient-text"></i>
                        <h3 class="text-2xl font-bold mb-2">粘贴文本</h3>
                        <p class="text-sm text-gray-500 mb-3">PASTE YOUR NOTES</p>
                        <p class="text-gray-700">将您的笔记、文章或任何文本内容粘贴至编辑框。</p>
                    </div>
                    <div class="step-item p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                        <div class="text-7xl font-extrabold highlight-gradient-purple-pink gradient-text mb-4">02</div>
                        <i class="fas fa-sliders-h text-5xl mb-4 highlight-gradient-purple-pink gradient-text"></i>
                        <h3 class="text-2xl font-bold mb-2">选择声音</h3>
                        <p class="text-sm text-gray-500 mb-3">CHOOSE A VOICE</p>
                        <p class="text-gray-700">从丰富的AI语音库中挑选最适合您内容的声音和语调。</p>
                    </div>
                    <div class="step-item p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                         <div class="text-7xl font-extrabold highlight-gradient-pink-orange gradient-text mb-4">03</div>
                        <i class="fas fa-download text-5xl mb-4 highlight-gradient-pink-orange gradient-text"></i>
                        <h3 class="text-2xl font-bold mb-2">生成播客</h3>
                        <p class="text-sm text-gray-500 mb-3">GENERATE & DOWNLOAD</p>
                        <p class="text-gray-700">点击生成，AI Vocal将即刻为您打造高质量播客音频，可直接预览或下载。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="py-16 md:py-24 bg-gray-50 rounded-xl">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-5xl sm:text-6xl font-extrabold mb-4"><span class="highlight-gradient-main gradient-text">他们都在用</span> AI Vocal</h2>
                <p class="text-lg text-gray-600 mb-12">WHAT OUR USERS SAY</p>
                <div class="p-8 md:p-12 rounded-xl shadow-2xl bg-white relative">
                    <i class="fas fa-quote-left text-6xl highlight-gradient-blue-purple gradient-text opacity-20 absolute top-4 left-4"></i>
                    <p class="text-xl md:text-2xl italic text-gray-800 mb-6">
                        "AI播客生成器太神奇了！我每周的博客文章现在都能轻松变成播客，语音质量非常棒。对于我们这种忙碌的内容创作者来说简直是完美工具！"
                    </p>
                    <p class="font-bold text-lg">L. Gomez</p>
                    <p class="text-sm highlight-gradient-main gradient-text">DIGITAL MARKETER</p>
                    <i class="fas fa-quote-right text-6xl highlight-gradient-purple-pink gradient-text opacity-20 absolute bottom-4 right-4"></i>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-16 md:py-24">
            <div class="max-w-3xl mx-auto px-4">
                <h2 class="text-4xl sm:text-5xl font-extrabold text-center mb-12">还有疑问？<br/><span class="text-lg text-gray-600 font-medium">FREQUENTLY ASKED QUESTIONS</span></h2>
                <div id="faq-accordion" class="space-y-1">
                    <div class="accordion-item">
                        <button class="accordion-button">AI播客生成器是什么，如何工作？</button>
                        <div class="accordion-content">
                            <p>AI播客生成器是一款免费在线工具，使用AI技术将书面文字转化为自然的播客音频。您只需粘贴文本，选择声音，即可生成。</p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button class="accordion-button">我需要录音或编辑技能吗？</button>
                        <div class="accordion-content">
                            <p>完全不需要！AI Vocal为您处理所有技术环节，无需麦克风或专业编辑软件。</p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button class="accordion-button">AI播客生成器真的免费吗？</button>
                        <div class="accordion-content">
                            <p>是的，AI Vocal提供的AI播客生成器核心功能完全免费，无需注册或付费即可使用。</p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button class="accordion-button">生成的音频可以下载吗？</button>
                        <div class="accordion-content">
                            <p>当然！您可以直接在线收听，也可以下载音频文件用于各种平台分享。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Final CTA Section -->
        <section id="final-cta" class="py-20 md:py-32 text-center bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-xl relative overflow-hidden">
             <div class="absolute -top-20 -left-20 w-64 h-64 rounded-full highlight-gradient-blue-purple opacity-30 filter blur-2xl"></div>
             <div class="absolute -bottom-20 -right-20 w-72 h-72 rounded-full highlight-gradient-purple-pink opacity-30 filter blur-2xl"></div>
            <div class="max-w-3xl mx-auto px-4 relative z-10">
                <h2 class="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6"><span class="highlight-gradient-main gradient-text">即刻体验</span><br/>AI播客的创作魔力</h2>
                <p class="text-lg md:text-xl text-gray-700 mb-10">YOUR IDEAS DESERVE TO BE HEARD.</p>
                <a href="https://aivocal.io/ai-podcast" class="btn-primary text-xl px-10 py-4">免费创作您的第一个AI播客</a>
                <p class="mt-6 text-sm text-gray-600">无需信用卡 | 快速上手</p>
            </div>
        </section>

        <footer class="text-center mt-16 py-8 border-t border-gray-200">
             <div class="mb-4">
                <a href="https://aivocal.io/privacy" class="text-gray-500 hover:text-black mx-2">隐私政策</a> |
                <a href="https://aivocal.io/terms" class="text-gray-500 hover:text-black mx-2">服务条款</a> |
                <a href="https://aivocal.io/blog" class="text-gray-500 hover:text-black mx-2">博客</a>
            </div>
            <div class="mb-4">
                <a href="https://discord.gg/Hz7W34mmep" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-black mx-3 text-xl"><i class="fab fa-discord"></i></a>
                <a href="https://x.com/aivocalio" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-black mx-3 text-xl"><i class="fab fa-twitter"></i></a>
                <a href="https://www.youtube.com/@aivocalio" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-black mx-3 text-xl"><i class="fab fa-youtube"></i></a>
                <a href="https://www.linkedin.com/company/aivocal/" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-black mx-3 text-xl"><i class="fab fa-linkedin"></i></a>
            </div>
            <p class="text-gray-500">© <span id="current-year"></span> AI Vocal. All rights reserved. Designed by JerryBob.</p>
        </footer>
    </div>

    <script>
        const dateElement = document.getElementById('current-date');
        const yearElement = document.getElementById('current-year');
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        if (dateElement) {
            dateElement.textContent = `${year}-${month}-${day}`;
        }
        if (yearElement) {
            yearElement.textContent = year;
        }

        // Accordion Script
        const accordionItems = document.querySelectorAll('.accordion-item');
        accordionItems.forEach(item => {
            const button = item.querySelector('.accordion-button');
            const content = item.querySelector('.accordion-content');
            button.addEventListener('click', () => {
                const isOpen = button.classList.contains('open');
                // Close all other items
                // accordionItems.forEach(otherItem => {
                //     if (otherItem !== item) {
                //         otherItem.querySelector('.accordion-button').classList.remove('open');
                //         otherItem.querySelector('.accordion-content').style.maxHeight = null;
                //     }
                // });

                if (isOpen) {
                    button.classList.remove('open');
                    content.style.maxHeight = null;
                } else {
                    button.classList.add('open');
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>