<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创造与连接 - The Meaning of Life</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #050810;
            color: #E2E8F0;
        }

        .glass-card {
            background: rgba(17, 24, 39, 0.3);
            backdrop-filter: blur(40px);
            -webkit-backdrop-filter: blur(40px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 2rem;
            transition: transform 0.3s ease-out;
            will-change: transform;
            z-index: 10;
            position: relative;
            overflow: hidden;
            padding: 2.5rem;
        }

        .glass-card > * { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5); }

        .blob-container {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            overflow: hidden; z-index: -1; filter: blur(100px);
        }

        .blob { position: absolute; border-radius: 50%; will-change: transform; }
        .blob-1 { background: #6D28D9; width: 30vw; height: 30vw; top: 5vh; left: 10vw; animation: move-blob-1 30s ease-in-out infinite; }
        .blob-2 { background: #D97706; width: 25vw; height: 25vw; top: 50vh; left: 65vw; animation: move-blob-2 35s ease-in-out infinite; }
        .blob-3 { background: #1D4ED8; width: 28vw; height: 28vw; top: 60vh; left: -5vw; animation: move-blob-3 25s ease-in-out infinite; }

        @keyframes move-blob-1 { 0%{transform:translate(0,0) scale(1)} 50%{transform:translate(20vw,30vh) scale(1.3)} 100%{transform:translate(0,0) scale(1)} }
        @keyframes move-blob-2 { 0%{transform:translate(0,0) scale(1)} 50%{transform:translate(-30vw,-40vh) scale(0.8)} 100%{transform:translate(0,0) scale(1)} }
        @keyframes move-blob-3 { 0%{transform:translate(0,0) scale(1)} 50%{transform:translate(40vw,-20vh) scale(1.1)} 100%{transform:translate(0,0) scale(1)} }

        .highlight-gradient-purple { background: linear-gradient(90deg, rgba(167, 139, 250, 0.6), rgba(167, 139, 250, 0)); }
        .highlight-gradient-amber { background: linear-gradient(90deg, rgba(251, 191, 36, 0.6), rgba(251, 191, 36, 0)); }

        /* SVG Animations */
        #creation-path {
            stroke-dasharray: 1500;
            stroke-dashoffset: 1500;
            animation: draw 8s ease-in-out forwards infinite alternate;
        }
        @keyframes draw { to { stroke-dashoffset: 0; } }

        #connection-dot1, #connection-dot2 { animation: move-dots 6s ease-in-out forwards infinite alternate; }
        #connection-dot2 { animation-delay: -3s; } /* Stagger animation */
        @keyframes move-dots {
            0% { r: 2; opacity: 0.5; }
            50% { r: 6; opacity: 1; }
            100% { r: 2; opacity: 0.5; }
        }
    </style>
</head>
<body class="w-full min-h-screen flex justify-center p-4 sm:p-8 lg:p-12">

    <div class="blob-container">
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
        <div class="blob blob-3"></div>
    </div>

    <main id="content-grid" class="w-full max-w-screen-2xl h-auto grid grid-cols-1 md:grid-cols-12 gap-8">

        <!-- Card 1: The Thesis -->
        <div class="glass-card col-span-1 md:col-span-12 flex flex-col justify-center items-center text-center">
            <p class="text-xl md:text-2xl text-white/70 tracking-widest">人生最大的意义，只产生于两个瞬间：</p>
            <div class="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-16 my-4">
                <h1 class="text-7xl sm:text-8xl lg:text-9xl font-black text-violet-300">创造</h1>
                <h1 class="text-7xl sm:text-8xl lg:text-9xl font-black text-amber-300">连接</h1>
            </div>
            <p class="text-base md:text-lg text-white/50 uppercase tracking-widest">THE MOMENTS OF CREATION & CONNECTION</p>
        </div>

        <!-- Card 2: Creation -->
        <div class="glass-card col-span-1 md:col-span-6 flex flex-col justify-between min-h-[500px] lg:min-h-[600px]">
            <div>
                <h2 class="text-5xl lg:text-6xl font-bold text-white">创造</h2>
                <span class="text-sm text-violet-300/80 uppercase tracking-widest">CREATION</span>
                <div class="highlight-gradient-purple h-0.5 w-2/3 mt-2"></div>
                <p class="mt-6 text-lg text-white/80 leading-relaxed">
                    用你的双手、你的思想，去做出一个世界上原本没有的东西。写一首烂诗，做一个粗糙的木工，提出一个傻问题，组织一次笨拙的聚会。这是你<span class="text-violet-300 font-semibold">对抗虚无</span>的唯一方式。
                </p>
            </div>
            <div class="w-full h-48 lg:h-64 flex justify-center items-center mt-6">
                <svg width="100%" height="100%" viewBox="0 0 300 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path id="creation-path" d="M20 75 C 40 10, 80 10, 100 75 S 160 140, 180 75 S 220 10, 240 75 S 280 140, 300 75" stroke="url(#purple-grad)" stroke-width="2.5" stroke-linecap="round"/>
                    <defs>
                        <linearGradient id="purple-grad" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="#C4B5FD"/>
                            <stop offset="100%" stop-color="#8B5CF6"/>
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>

        <!-- Card 3: Connection -->
        <div class="glass-card col-span-1 md:col-span-6 flex flex-col justify-between min-h-[500px] lg:min-h-[600px]">
            <div>
                <h2 class="text-5xl lg:text-6xl font-bold text-white">连接</h2>
                <span class="text-sm text-amber-300/80 uppercase tracking-widest">CONNECTION</span>
                <div class="highlight-gradient-amber h-0.5 w-2/3 mt-2"></div>
                <p class="mt-6 text-lg text-white/80 leading-relaxed">
                    与另一个生命体，产生深刻的、无功利的交集。爱一个人，养一只猫，聆听一个朋友的痛苦，理解一个陌生人的善意。这是你<span class="text-amber-300 font-semibold">确认自己存在</span>的唯一回声。
                </p>
            </div>
            <div class="w-full h-48 lg:h-64 flex justify-center items-center mt-6">
                 <svg width="100%" height="100%" viewBox="0 0 300 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path id="path1" d="M 30 120 C 80 10, 220 10, 270 120" stroke="rgba(251, 191, 36, 0.2)" stroke-width="1.5" stroke-dasharray="4 4" />
                    <path id="path2" d="M 30 30 C 80 140, 220 140, 270 30" stroke="rgba(251, 191, 36, 0.2)" stroke-width="1.5" stroke-dasharray="4 4" />
                    <circle id="connection-dot1" cx="30" cy="120" r="4" fill="#FBBF24">
                        <animateMotion dur="6s" repeatCount="indefinite" path="M 30 120 C 80 10, 220 10, 270 120" />
                    </circle>
                    <circle id="connection-dot2" cx="30" cy="30" r="4" fill="#FBBF24">
                        <animateMotion dur="6s" repeatCount="indefinite" path="M 30 30 C 80 140, 220 140, 270 30" />
                    </circle>
                </svg>
            </div>
        </div>

        <!-- Card 4: The Conclusion -->
        <div class="glass-card col-span-1 md:col-span-12 flex flex-col justify-center items-center text-center p-8">
            <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold">意义在哪里？</h3>
            <p class="mt-4 text-xl md:text-2xl text-white/80">就在你<span class="text-violet-300">有所创造</span>，和<span class="text-amber-300">有所深爱</span>的地方。</p>
            <p class="mt-2 text-base text-white/50 uppercase tracking-widest">Nowhere else.</p>
        </div>

    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.glass-card');
            const parallaxIntensity = 8;
            document.body.addEventListener('mousemove', (e) => {
                // Throttle event
                window.requestAnimationFrame(() => {
                    const x = (e.clientX / window.innerWidth) - 0.5;
                    const y = (e.clientY / window.innerHeight) - 0.5;
                    cards.forEach(card => {
                        const transformX = x * parallaxIntensity;
                        const transformY = y * parallaxIntensity;
                        card.style.transform = `translate(${transformX}px, ${transformY}px)`;
                    });
                });
            });
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>