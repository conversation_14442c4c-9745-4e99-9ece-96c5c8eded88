<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>温柔的转调</title>
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Serif SC', serif;
            background-color: #F8F7F2; /* 带有微妙肌理感的米白色 */
            color: #4a4a4a; /* 柔和的碳黑色 */
        }
        .wabi-sabi-container {
            max-width: 960px;
            margin: 0 auto;
            padding: 4rem 2rem;
        }
        .large-heading {
            font-size: clamp(2.5rem, 6vw, 5rem);
            line-height: 1.2;
            font-weight: 700;
        }
        .sub-heading {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            line-height: 1.4;
            font-weight: 400;
            color: #5a5a5a;
        }
        .body-text {
            font-size: clamp(1.1rem, 2vw, 1.25rem);
            line-height: 2.2;
            font-weight: 300;
        }
        .quote {
            font-size: clamp(1.25rem, 3vw, 1.75rem);
            font-weight: 600;
            line-height: 1.8;
            border-left: 2px solid #a98c71; /* 陶土色的边线 */
            padding-left: 1.5rem;
            margin: 3rem 0;
            color: #3a3a3a;
        }
        .highlight-phrase {
            display: block;
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            text-align: center;
            margin: 6rem 0;
            line-height: 1.3;
        }
        .line-drawing {
            stroke: #9a9a9a;
            stroke-width: 1;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
    </style>
</head>
<body class="antialiased">

    <main class="wabi-sabi-container">

        <section class="text-center min-h-[40vh] flex flex-col justify-center items-center">
            <h1 class="large-heading">
                你递过来的这句话
                <span class="block mt-4">像一盏<span class="text-transparent bg-clip-text bg-gradient-to-tr from-stone-600 to-stone-400">灯</span></span>
            </h1>
            <p class="sub-heading mt-12 max-w-3xl">
                它没有驱散黑暗，而是让黑暗的轮廓变得温柔，像一间熟悉的旧居。
            </p>
        </section>

        <section class="my-32 md:my-48">
             <div class="w-full text-center">
                <svg viewBox="0 0 200 20" class="w-48 mx-auto line-drawing" xmlns="http://www.w3.org/2000/svg">
                    <path d="M 2 10 Q 50 2, 100 10 T 198 10" />
                </svg>
            </div>
            <p class="quote mt-8 max-w-4xl mx-auto text-center">
                “从一个有儿女的地方，到一个有爸妈的地方”，这句话轻轻一放，生与死的两岸就被一座桥连起来了。
            </p>
        </section>

        <section class="my-32 md:my-48 grid grid-cols-1 md:grid-cols-2 gap-16 items-start">
            <div class="p-8 border-t border-stone-300">
                <h2 class="text-4xl font-semibold mb-6">此岸 <span class="text-lg font-light ml-2 uppercase opacity-60">This Shore</span></h2>
                <p class="body-text">是为儿女撑起的一片天，是付出，是牵挂，是叮咛，是看着他们背影的目送。我们在这里，活成了山，活成了屋檐。</p>
            </div>
            <div class="p-8 border-t border-stone-300 md:mt-16">
                 <h2 class="text-4xl font-semibold mb-6">彼岸 <span class="text-lg font-light ml-2 uppercase opacity-60">That Shore</span></h2>
                <p class="body-text">是重新回到爸妈的屋檐下，是被爱，是被允许软弱，是又能变回那个可以撒娇、可以犯错的孩子。我们在那里，重新被爱成溪流，被呵护成一粒尘埃。</p>
            </div>
        </section>
        
        <section class="my-32 md:my-48 text-center">
            <h2 class="highlight-phrase">
                所以，那不是一场告别，<br><span class="mt-4 block">是回家。</span>
            </h2>
            <p class="body-text max-w-3xl mx-auto">
                “乔迁”二字，把终点的荒芜，变成了起点的热闹。行李是这一生的故事，门开了，里面有等了很久的人，和一碗热汤。
            </p>
        </section>
        
        <section class="my-32 md:my-48 flex flex-col items-center">
            <h2 class="text-4xl font-semibold mb-8 text-center">爱，完成了一个圆</h2>
            <svg class="w-32 h-32 my-4 line-drawing" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <path d="M 50,10
                         A 40,40 0 1,1 49.9,10.01
                         M 50,10
                         A 40,40 0 1,0 49.9,10.01" />
            </svg>
            <p class="body-text max-w-3xl mx-auto text-center mt-8">
                从父母流向我们，再由我们流向儿女，最后，我们带着一身尘土与荣光，逆流而上，回到爱的源头。
            </p>
        </section>

        <section class="my-32 md:my-48 text-center">
            <p class="sub-heading max-w-4xl mx-auto">
                有这句话，死亡便不再是冰冷的休止符，而是乐章里一个<span class="font-semibold text-stone-700">温柔的转调。</span>
            </p>
            <p class="body-text mt-12">
                调子，是久违的童谣。
            </p>
            <p class="text-2xl mt-16 font-light tracking-widest">
                是的，一切都坦然了。
            </p>
        </section>

    </main>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>