<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>如何治愈拖延症 - 一场心灵手术</title>
    
    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com/3.4.1"></script>

    <!-- Google Fonts: Noto Sans SC -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <style>
        /* --- 全局及背景核心样式 --- */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #030014; /* 深邃的底色 */
            color: #e5e7eb; /* 默认文字颜色 */
            overflow-x: hidden;
        }

        /* 动态霓虹光晕背景容器 */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -10;
        }

        /* 霓虹光球 */
        .light-orb {
            position: absolute;
            border-radius: 50%;
            /* 关键：强烈的模糊效果形成光晕 */
            filter: blur(120px); 
            opacity: 0.6;
        }

        @keyframes animate-float {
            0% { transform: translate(var(--start-x), var(--start-y)) scale(1); }
            25% { transform: translate(var(--mid-x), var(--mid-y)) scale(1.3); }
            50% { transform: translate(var(--end-x), var(--end-y)) scale(0.9); }
            75% { transform: translate(var(--mid-y), var(--mid-x)) scale(1.2); }
            100% { transform: translate(var(--start-x), var(--start-y)) scale(1); }
        }

        .orb-1 { animation: animate-float 38s ease-in-out infinite; }
        .orb-2 { animation: animate-float 48s ease-in-out infinite reverse; }
        .orb-3 { animation: animate-float 42s ease-in-out infinite; }

        /* 前景长虹玻璃效果叠加层 */
        main::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            /* 关键：用重复线性渐变模拟粗犷的竖向棱纹 */
            background-image: repeating-linear-gradient(90deg, 
                rgba(0, 0, 0, 0.2) 0px, 
                rgba(0, 0, 0, 0.2) 4px, /* 增加棱的粗度 */
                transparent 4px, 
                transparent 10px /* 增加棱的间距 */
            );
            /* 关键：用 backdrop-filter 扭曲后方光晕 */
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            z-index: -5;
            pointer-events: none;
        }

        /* --- 前景元素及动画 --- */

        /* 滚动入场动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1s cubic-bezier(0.16, 1, 0.3, 1), transform 1s cubic-bezier(0.16, 1, 0.3, 1);
            transition-delay: 0.2s;
        }
        .scroll-animate.is-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* "液态"毛玻璃卡片 */
        .liquid-card {
            background: rgba(20, 20, 35, 0.25);
            backdrop-filter: blur(32px);
            -webkit-backdrop-filter: blur(32px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            /* 关键：圆润的边角营造流动感 */
            border-radius: 32px; 
            box-shadow: 0 16px 60px rgba(0, 0, 0, 0.4);
            transition: transform 0.4s ease, box-shadow 0.4s ease;
        }
        .liquid-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 24px 80px rgba(0, 0, 0, 0.5);
        }

        /* 高亮色 & 渐变效果 */
        .highlight-pink {
            color: #f472b6; /* 赛博朋克粉 */
        }
        .highlight-blue {
            color: #60a5fa; /* 电光蓝 */
        }
        
        /* 标题下划线 - 模拟辉光 */
        .title-underline {
            position: relative;
            padding-bottom: 1rem;
        }
        .title-underline::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            /* 关键：使用高亮色，两端透明度为0，中间为1，制造科技感渐变 */
            background: linear-gradient(90deg, rgba(244, 114, 182, 0) 0%, rgba(244, 114, 182, 1) 50%, rgba(244, 114, 182, 0) 100%);
            border-radius: 2px;
            filter: blur(2px); /* 轻微模糊增加辉光感 */
        }
        
        /* 独特的引用/重点框样式 */
        .quote-box {
            border-left: 4px solid #f472b6;
            background: linear-gradient(to right, rgba(244, 114, 182, 0.15), rgba(244, 114, 182, 0));
        }

    </style>
</head>
<body class="antialiased">

    <!-- 背景：动态霓虹光球 -->
    <div class="background-container">
        <!-- 赛博朋克粉 -->
        <div class="light-orb orb-1" style="width: 60vw; height: 60vw; background-color: #ec4899; --start-x: -20vw; --start-y: -20vh; --mid-x: 50vw; --mid-y: 80vh; --end-x: 10vw; --end-y: 10vh;"></div>
        <!-- 电光蓝 -->
        <div class="light-orb orb-2" style="width: 50vw; height: 50vw; background-color: #3b82f6; --start-x: 70vw; --start-y: 10vh; --mid-x: 0vw; --mid-y: 60vh; --end-x: 80vw; --end-y: 90vh;"></div>
        <!-- 迷幻紫 -->
        <div class="light-orb orb-3" style="width: 40vw; height: 40vw; background-color: #a855f7; --start-x: 10vw; --start-y: 70vh; --mid-x: 80vw; --mid-y: 20vh; --end-x: -10vw; --end-y: 50vh;"></div>
    </div>

    <main class="relative z-10">
        <div class="container mx-auto px-6 lg:px-8 py-24 lg:py-40 space-y-24 lg:space-y-48">

            <!-- Section 1: Hero -->
            <section class="text-center min-h-screen flex flex-col justify-center items-center scroll-animate">
                <p class="text-xl lg:text-2xl font-light text-gray-400 uppercase tracking-widest mb-4">THE PROCRASTINATION CURE</p>
                <h1 class="text-5xl md:text-7xl lg:text-9xl font-black leading-tight tracking-tighter">
                    如何治愈<span class="highlight-pink">拖延症</span>
                </h1>
                <p class="mt-8 max-w-3xl mx-auto text-lg lg:text-2xl text-gray-300 font-light">
                    这可能是我们这一代人，内心最普遍、也最痛苦的挣扎。
                </p>
            </section>

            <!-- Section 2: 核心事实 -->
            <section class="text-center max-w-4xl mx-auto scroll-animate">
                <p class="text-lg lg:text-xl font-light text-gray-400">首先，请你接受一个最重要的事实</p>
                <h2 class="text-4xl md:text-6xl lg:text-8xl font-black my-6">
                    拖延症，不是<span class="highlight-blue">懒</span>
                </h2>
                <p class="text-lg lg:text-xl text-gray-300">
                    也不是意志力薄弱。如果你总是在自责，那么你永远也治不好它。因为你从一开始就找错了敌人。
                </p>
                <div class="mt-12 p-8 quote-box">
                    <p class="text-2xl lg:text-4xl font-bold leading-relaxed">
                        拖延，本质上是一种情绪调节问题。
                    </p>
                    <p class="mt-4 text-gray-300 lg:text-xl">
                        它是你的大脑在面对让你不适的任务时，为了让你立刻感觉好一点，而选择的一种<strong class="font-bold text-white">“逃避机制”</strong>。
                    </p>
                </div>
            </section>
            
            <!-- Section 3: 手术指南介绍 -->
            <section class="text-center max-w-4xl mx-auto scroll-animate">
                <h2 class="text-4xl md:text-5xl font-black title-underline">心灵手术指南</h2>
                <p class="mt-8 text-xl lg:text-2xl text-gray-300 font-light">
                    治愈拖延症，不是一场意志力的战斗，而是一场与自己情绪和解、并重建行为系统的“心灵手术”。
                </p>
                <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
                    <div class="p-6 border-l-2 border-pink-500">
                        <p class="text-sm text-pink-400 font-bold">STEP 01</p>
                        <p class="text-2xl font-bold mt-1">诊断病因</p>
                    </div>
                    <div class="p-6 border-l-2 border-blue-500">
                        <p class="text-sm text-blue-400 font-bold">STEP 02</p>
                        <p class="text-2xl font-bold mt-1">重塑心法</p>
                    </div>
                    <div class="p-6 border-l-2 border-orange-400">
                        <p class="text-sm text-orange-400 font-bold">STEP 03</p>
                        <p class="text-2xl font-bold mt-1">系统化行动</p>
                    </div>
                </div>
            </section>

            <!-- Section 4: 第一步 - 诊断病因 -->
            <section class="scroll-animate">
                <div class="text-center mb-16">
                    <p class="text-blue-400 font-bold">STEP 01</p>
                    <h2 class="text-4xl md:text-6xl font-black mt-2">诊断病因</h2>
                    <p class="mt-4 text-xl lg:text-2xl text-gray-300 font-light">你的拖延，是哪只“情绪怪兽”？</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8 lg:gap-12">
                    <div class="liquid-card p-8 flex flex-col items-center text-center">
                        <i class="fas fa-crown text-5xl highlight-pink mb-6"></i>
                        <h3 class="text-2xl font-bold mb-3">完美主义怪兽</h3>
                        <p class="text-gray-300 font-light">“这件事我必须做到最好，否则就不如不做。” 因害怕达不到虚高的“最好”，迟迟无法开始。</p>
                    </div>
                    <div class="liquid-card p-8 flex flex-col items-center text-center">
                        <i class="fas fa-ghost text-5xl highlight-blue mb-6"></i>
                        <h3 class="text-2xl font-bold mb-3">恐惧怪兽</h3>
                        <p class="text-gray-300 font-light">“如果我失败了怎么办？别人会怎么看我？” 害怕失败与被评判，让你宁愿停在原地。</p>
                    </div>
                    <div class="liquid-card p-8 flex flex-col items-center text-center">
                        <i class="fas fa-mountain text-5xl text-purple-400 mb-6"></i>
                        <h3 class="text-2xl font-bold mb-3">迷茫怪兽</h3>
                        <p class="text-gray-300 font-light">“任务太大了，我根本不知道从哪儿下手。” 压倒性的无力感，让你选择视而不见。</p>
                    </div>
                    <div class="liquid-card p-8 flex flex-col items-center text-center">
                        <i class="fas fa-bed text-5xl text-green-400 mb-6"></i>
                        <h3 class="text-2xl font-bold mb-3">无聊怪兽</h3>
                        <p class="text-gray-300 font-light">“这件事太没意思了，一点劲都提不起来。” 缺乏内在驱动力和即时反馈，让你无法启动。</p>
                    </div>
                </div>
            </section>
            
            <!-- Section 5: 第二步 - 重塑心法 -->
            <section class="scroll-animate">
                <div class="text-center mb-16">
                    <p class="text-pink-400 font-bold">STEP 02</p>
                    <h2 class="text-4xl md:text-6xl font-black mt-2">重塑心法</h2>
                    <p class="mt-4 text-xl lg:text-2xl text-gray-300 font-light">改变你与任务的关系，赢得内在战争</p>
                </div>
                <div class="space-y-12">
                    <div class="liquid-card p-8 lg:p-12">
                        <h3 class="text-3xl font-bold mb-4 highlight-pink">接纳与自我原谅 <span class="text-lg font-light text-gray-300 ml-2 uppercase">The Most Important Mindset</span></h3>
                        <p class="text-lg text-gray-200">首先，原谅自己过去的每一次拖延。负罪感和自责只会消耗你的心理能量。对自己说：“是的，我拖延了。但这不代表我是一个失败的人。现在，我可以重新开始。”</p>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="liquid-card p-8">
                            <h3 class="text-3xl font-bold mb-4 highlight-blue">把“完成”变成“开始”</h3>
                            <p class="text-lg text-gray-200 mb-6">不要想：“我要写完这篇<span class="text-2xl font-black text-white">5000字</span>的报告。”</p>
                            <p class="text-lg text-gray-200">而是想：“我只要打开文档，写下<span class="text-2xl font-black text-white">第一个标题</span>就行。”</p>
                        </div>
                        <div class="liquid-card p-8">
                            <h3 class="text-3xl font-bold mb-4 text-purple-400">拥抱“完成好过完美”</h3>
                            <div class="flex items-center justify-around mt-6">
                                <div class="text-center">
                                    <p class="text-7xl font-black text-green-400">60<span class="text-4xl">%</span></p>
                                    <p class="mt-2 font-bold text-white">已完成的草稿</p>
                                </div>
                                <p class="text-4xl font-thin text-gray-400">></p>
                                <div class="text-center opacity-40">
                                    <p class="text-7xl font-black">100<span class="text-4xl">%</span></p>
                                    <p class="mt-2 font-bold">想象中的杰作</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 6: 第三步 - 系统化行动 -->
            <section class="scroll-animate">
                <div class="text-center mb-16">
                    <p class="text-orange-400 font-bold">STEP 03</p>
                    <h2 class="text-4xl md:text-6xl font-black mt-2">系统化行动</h2>
                    <p class="mt-4 text-xl lg:text-2xl text-gray-300 font-light">让“开始”变得像呼吸一样简单</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 卡片1: 两分钟法则 -->
                    <div class="liquid-card p-8 row-span-1">
                        <i class="fas fa-rocket text-4xl highlight-pink mb-4"></i>
                        <h3 class="text-3xl font-bold mb-3">“两分钟法则”</h3>
                        <p class="text-lg text-gray-300 mb-4">任何任务，都找到一个能在两分钟内完成的“启动版本”。</p>
                        <ul class="space-y-2 text-gray-200">
                            <li><i class="fas fa-shoe-prints text-pink-400 mr-3"></i>“每天跑步” → “穿上我的跑鞋”</li>
                            <li><i class="fas fa-tshirt text-pink-400 mr-3"></i>“整理房间” → “把一件衣服放回衣柜”</li>
                            <li><i class="fas fa-book-open text-pink-400 mr-3"></i>“读一本书” → “读完第一页”</li>
                        </ul>
                    </div>
                    <!-- 卡片2: 环境设计 -->
                    <div class="liquid-card p-8 row-span-1">
                        <i class="fas fa-drafting-compass text-4xl highlight-blue mb-4"></i>
                        <h3 class="text-3xl font-bold mb-3">环境设计</h3>
                        <p class="text-lg text-gray-300 mb-4">让自律变得多余。增加分心阻力，减少开始阻力。</p>
                        <div class="flex justify-between">
                            <div class="text-center p-2">
                                <i class="fas fa-ban text-red-500 text-3xl"></i>
                                <p class="mt-2 text-sm">增加阻力<br>(手机放远)</p>
                            </div>
                            <div class="text-center p-2">
                                <i class="fas fa-arrow-right text-gray-400 text-3xl self-center"></i>
                            </div>
                            <div class="text-center p-2">
                                <i class="fas fa-check-circle text-green-500 text-3xl"></i>
                                <p class="mt-2 text-sm">减少阻力<br>(书放枕边)</p>
                            </div>
                        </div>
                    </div>
                    <!-- 卡片3: 任务分解 -->
                    <div class="liquid-card p-8 lg:col-span-2">
                        <i class="fas fa-tasks text-4xl text-purple-400 mb-4"></i>
                        <h3 class="text-3xl font-bold mb-3">任务分解 <span class="text-lg font-light text-gray-300 ml-2 uppercase">Eat The Elephant</span></h3>
                        <p class="text-lg text-gray-300 mb-6">把让你望而生畏的大任务，拆解成清晰的“待办清单”。</p>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div class="border border-purple-400/50 rounded-lg p-3 bg-purple-500/10">
                                <p class="font-bold">核心观点</p><p class="text-sm text-gray-300">5分钟</p>
                            </div>
                            <div class="border border-purple-400/50 rounded-lg p-3 bg-purple-500/10">
                                <p class="font-bold">列出分论点</p><p class="text-sm text-gray-300">10分钟</p>
                            </div>
                            <div class="border border-purple-400/50 rounded-lg p-3 bg-purple-500/10">
                                <p class="font-bold">寻找配图</p><p class="text-sm text-gray-300">15分钟</p>
                            </div>
                            <div class="border border-purple-400/50 rounded-lg p-3 bg-purple-500/10">
                                <p class="font-bold">套用模板</p><p class="text-sm text-gray-300">5分钟</p>
                            </div>
                        </div>
                    </div>
                     <!-- 卡片4: 番茄工作法 -->
                    <div class="liquid-card p-8 lg:col-span-2">
                        <i class="fas fa-stopwatch-20 text-4xl text-green-400 mb-4"></i>
                        <h3 class="text-3xl font-bold mb-3">番茄工作法</h3>
                        <p class="text-lg text-gray-300 mb-6">创造紧迫感与奖赏，对抗无聊和注意力涣散。</p>
                        <div class="flex items-center justify-center space-x-4 md:space-x-8">
                             <div class="text-center">
                                <p class="text-5xl lg:text-7xl font-black text-green-400">25</p>
                                <p class="mt-2 text-lg font-bold">专注工作</p>
                            </div>
                            <i class="fas fa-sync-alt text-4xl text-gray-500 fa-spin" style="animation-duration: 25s;"></i>
                             <div class="text-center">
                                <p class="text-5xl lg:text-7xl font-black text-blue-400">5</p>
                                <p class="mt-2 text-lg font-bold">安心休息</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Section 7: 最后的灵魂一问 -->
            <section class="text-center max-w-4xl mx-auto scroll-animate py-24">
                 <h2 class="text-4xl md:text-5xl font-black title-underline">最后的“灵魂一问”</h2>
                <p class="mt-12 text-2xl lg:text-3xl text-gray-200 leading-relaxed">
                    现在，忘掉“治愈拖延症”这个宏大的目标。<br>看着你清单上最拖延的那件事，然后问自己：
                </p>
                <div class="my-16">
                     <p class="text-2xl lg:text-4xl font-light text-gray-200">
                        “在接下来的
                        <span class="text-6xl lg:text-8xl font-black highlight-pink mx-2">5</span>
                        分钟内，<br>为了这件事，我能完成的、最微不足道、简单到可笑的第一步是什么？”
                    </p>
                </div>
                <h3 class="text-5xl md:text-7xl lg:text-8xl font-black tracking-tighter">
                    找到了吗？<br>
                    <span class="mt-6 block bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-blue-500 to-pink-500">现在，就去做。</span>
                </h3>
            </section>

        </div>
    </main>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // --- 滚动入场动画 Intersection Observer ---
        const scrollElements = document.querySelectorAll('.scroll-animate');

        const elementInView = (el, dividend = 1) => {
            const elementTop = el.getBoundingClientRect().top;
            return (
                elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend
            );
        };

        const displayScrollElement = (element) => {
            element.classList.add('is-visible');
        };

        const hideScrollElement = (element) => {
            element.classList.remove('is-visible');
        };

        const handleScrollAnimation = () => {
            scrollElements.forEach((el) => {
                if (elementInView(el, 1.25)) {
                    displayScrollElement(el);
                } 
                // 可选：如果希望元素滚出视口后隐藏，然后再次滚入时重新播放动画，请取消下面注释
                // else {
                //     hideScrollElement(el);
                // }
            })
        }

        window.addEventListener('scroll', () => {
            handleScrollAnimation();
        });

        // 首次加载时触发一次，以显示视口内已有的元素
        handleScrollAnimation();


        // --- 背景光球随机化 ---
        const orbs = document.querySelectorAll('.light-orb');
        orbs.forEach(orb => {
            // 为每个光球的动画添加随机延迟，使它们的运动错开，更自然
            orb.style.animationDelay = `${Math.random() * -10}s`;
        });
    });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>