<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间的本质：一场意识与宇宙的共舞</title>
    <script src="https://cdn.tailwindcss.com/3.4.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #02040A;
            color: #E0E7FF;
            overflow-x: hidden;
        }

        .hybrid-grid-card {
            background: rgba(12, 18, 38, 0.5);
            -webkit-backdrop-filter: blur(50px);
            backdrop-filter: blur(50px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            transition: all 0.3s ease;
        }

        /* 动态背景光斑 */
        .blur-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(160px);
            opacity: 0.5;
            pointer-events: none;
        }

        .blob-1 {
            width: 800px;
            height: 800px;
            background: #00416A; /* Deep Cosmic Blue */
            top: -25%;
            left: -30%;
            animation: move-blob-1 60s infinite alternate ease-in-out;
        }

        .blob-2 {
            width: 700px;
            height: 700px;
            background: #00E5FF; /* Ethereal Cyan */
            bottom: -30%;
            right: -35%;
            animation: move-blob-2 55s infinite alternate ease-in-out;
        }

        @keyframes move-blob-1 {
            from { transform: translate(0, 0) scale(1); }
            to { transform: translate(200px, 150px) scale(1.3); }
        }
        @keyframes move-blob-2 {
            from { transform: translate(0, 0) scale(1); }
            to { transform: translate(-250px, -180px) scale(1.2); }
        }

        /* 鼠标跟随光晕 */
        #cursor-light {
            position: fixed;
            width: 800px;
            height: 800px;
            left: 0;
            top: 0;
            transform: translate(-50%, -50%);
            background: radial-gradient(circle, rgba(0, 229, 255, 0.1) 0%, rgba(0, 229, 255, 0) 50%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 0;
        }

        /* 入场动画 */
        .reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 1.5s cubic-bezier(0.19, 1, 0.22, 1), transform 1.5s cubic-bezier(0.19, 1, 0.22, 1);
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .glow-icon {
             filter: drop-shadow(0 0 10px currentColor);
             animation: breath-icon 6s infinite ease-in-out;
        }
        @keyframes breath-icon {
            0%, 100% { filter: drop-shadow(0 0 10px currentColor); opacity: 0.8; }
            50% { filter: drop-shadow(0 0 20px currentColor); opacity: 1; }
        }
    </style>
</head>
<body class="antialiased font-light">

    <!-- 动态背景与光晕 -->
    <div class="fixed inset-0 z-[-1] overflow-hidden">
        <div class="blur-blob blob-1"></div>
        <div class="blur-blob blob-2"></div>
    </div>
    <div id="cursor-light"></div>

    <div class="relative min-h-screen w-full py-20 px-4 sm:px-6 lg:px-8 z-10">
        <main class="max-w-7xl mx-auto flex flex-col gap-28 md:gap-40">

            <!-- Hero Section -->
            <section class="text-center flex flex-col items-center justify-center min-h-[70vh] reveal">
                <i class="fa-solid fa-atom text-8xl lg:text-9xl text-cyan-300 mb-8 glow-icon" style="animation-duration: 8s;"></i>
                <h1 class="text-5xl sm:text-7xl lg:text-8xl font-black tracking-tight text-white leading-tight">
                    时间是幻觉还是真实?
                </h1>
                <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-300 mt-4">A Dance of Consciousness and the Cosmos</p>
            </section>

            <!-- The Symphony Metaphor -->
            <section class="reveal">
                <div class="text-center mb-16">
                    <h2 class="text-5xl md:text-7xl font-bold text-white">宇宙交响乐</h2>
                    <p class="mt-4 text-gray-400">The Cosmic Symphony</p>
                </div>
                <div class="grid md:grid-cols-2 gap-8 items-stretch">
                    <div class="hybrid-grid-card rounded-3xl p-8 lg:p-12 flex flex-col text-center items-center border-blue-400/20">
                        <i class="fa-solid fa-infinity text-7xl text-blue-300 mb-6 glow-icon"></i>
                        <h3 class="text-4xl font-bold text-white">物理时间 | 乐谱</h3>
                        <p class="mt-4 text-gray-300">一份完整的乐谱。所有音符（时空事件）都同时、完整地写在上面。它是“永恒”的，是“块状”的宇宙。</p>
                    </div>
                    <div class="hybrid-grid-card rounded-3xl p-8 lg:p-12 flex flex-col text-center items-center border-cyan-400/20">
                        <i class="fa-solid fa-brain text-7xl text-cyan-300 mb-6 glow-icon"></i>
                        <h3 class="text-4xl font-bold text-white">心理时间 | 聆听</h3>
                        <p class="mt-4 text-gray-300">你聆听乐谱的过程。你只能一个音符一个音符地去听，你的意识赋予了静态乐谱以“生命”和“意义”。</p>
                    </div>
                </div>
            </section>

            <!-- The Duality -->
            <section class="reveal">
                 <div class="text-center mb-12">
                    <h2 class="text-5xl md:text-7xl font-bold text-white">一种二元结构</h2>
                    <p class="mt-4 text-gray-400">A Duality of Existence</p>
                </div>
                <div class="grid md:grid-cols-5 gap-8 items-center">
                    <div class="md:col-span-3 hybrid-grid-card rounded-3xl p-8">
                        <h3 class="text-3xl font-bold text-blue-300">物理层面</h3>
                        <p class="mt-4 text-gray-300">一个客观存在的、四维时空的“可能性画卷”。它是宇宙的基本维度，是变化的背景和舞台。</p>
                    </div>
                    <div class="md:col-span-2 hybrid-grid-card rounded-3xl p-8">
                        <h3 class="text-3xl font-bold text-cyan-300">意识层面</h3>
                        <p class="mt-4 text-gray-300">我们感知画卷的方式，我们体验“变化”的过程。</p>
                    </div>
                </div>
                 <p class="text-center mt-10 text-xl text-gray-400">记忆构成了“过去”，预判构成了“未来”，而感知的局限创造了稍纵即逝的“现在”。</p>
            </section>

            <!-- The Conclusion -->
            <section class="reveal">
                 <div class="hybrid-grid-card rounded-3xl p-10 md:p-16 text-center">
                    <h2 class="text-4xl font-bold text-white mb-8">我们是意识的小船</h2>
                    <i class="fa-solid fa-sailboat text-6xl text-white mb-8 glow-icon"></i>
                    <p class="max-w-3xl mx-auto text-lg text-gray-300">
                        我们并没有被困在时间的河流里随波逐流。我们更像是这艘名叫“意识”的小船，航行在名为“时空”的、早已存在的广阔海洋上。我们无法改变海洋本身，但我们航行的轨迹，我们对沿途风景的每一次感知和体验，构成了我们独一无二、真实不虚的人生。
                    </p>
                 </div>
            </section>

            <!-- The Final Question -->
            <section class="text-center py-16 reveal">
                 <h2 class="text-2xl md:text-3xl font-light text-gray-400">或许，我们不该再问“时间的本质是什么”</h2>
                 <p class="text-2xl md:text-3xl font-light text-gray-400 mt-2">而应该问：</p>
                 
                 <div class="my-16">
                     <i class="fa-solid fa-music text-8xl text-cyan-300 glow-icon" style="animation-duration: 4s;"></i>
                 </div>
                 
                 <h1 class="text-4xl sm:text-5xl md:text-7xl font-black text-white leading-tight">
                    在这场只能体验一次的宇宙交响乐中，<br class="hidden md:block"/>我该如何专注地、尽情地，<br class="hidden md:block"/>聆听好属于我的每一个音符？
                 </h1>
            </section>

        </main>
    </div>

    <script>
        const cursorLight = document.getElementById('cursor-light');
        document.addEventListener('mousemove', (e) => {
            cursorLight.style.left = e.clientX + 'px';
            cursorLight.style.top = e.clientY + 'px';
        });

        const revealElements = document.querySelectorAll('.reveal');
        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, { threshold: 0.1 });

        revealElements.forEach(el => {
            revealObserver.observe(el);
        });
    </script>
    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>