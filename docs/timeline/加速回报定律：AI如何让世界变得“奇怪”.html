<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加速回报定律：AI如何让世界变得“奇怪”</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #0a0a0f;
            color: #e0e0e0;
            background-image: 
                linear-gradient(rgba(0, 246, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 246, 255, 0.03) 1px, transparent 1px);
            background-size: 30px 30px;
        }

        .highlight-cyan {
            color: #00f6ff;
        }

        .glow-text {
            text-shadow: 0 0 8px rgba(0, 246, 255, 0.6), 0 0 20px rgba(0, 246, 255, 0.4);
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            line-height: 1.1;
            letter-spacing: -0.02em;
        }

        .section-title {
            font-size: 2.8rem;
            font-weight: 700;
        }
        
        .card {
            background-color: rgba(18, 18, 28, 0.7);
            border: 1px solid rgba(0, 246, 255, 0.2);
            backdrop-filter: blur(8px);
            transition: all 0.3s ease;
        }

        .card:hover {
            border-color: rgba(0, 246, 255, 0.5);
            box-shadow: 0 0 25px rgba(0, 246, 255, 0.15);
            transform: translateY(-5px);
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 246, 255, 0.5), transparent);
            margin: 4rem 0;
        }

        .accent-gradient-text {
            background: linear-gradient(90deg, #00f6ff, #5d8aff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .vertical-line {
            width: 3px;
            background: linear-gradient(180deg, rgba(0, 246, 255, 0), rgba(0, 246, 255, 0.4), rgba(0, 246, 255, 0));
        }

        @media (min-width: 1024px) {
            .hero-title {
                font-size: 6.5rem;
            }
            .section-title {
                font-size: 3.5rem;
            }
        }
    </style>
</head>
<body class="antialiased">

    <div class="container mx-auto px-6 py-16 md:py-24 max-w-7xl">

        <!-- Hero Section -->
        <header class="text-center mb-24 md:mb-32">
            <h1 class="hero-title mb-4">
                <span class="accent-gradient-text">加速回报定律</span>
            </h1>
            <p class="text-sm md:text-base font-light uppercase tracking-widest text-gray-400 mb-6">The Law of Accelerating Returns</p>
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                我们正处于历史加速的“奇点”前夜，而AI，就是那个将油门踩到底的引擎。为什么这个定律在今天变得如此<span class="highlight-cyan font-bold glow-text">“奇怪”</span>和强大？
            </p>
        </header>

        <!-- Section 1: Time Collapse -->
        <section class="mb-24 md:mb-32">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <div class="lg:col-span-1">
                    <h2 class="section-title mb-4 highlight-cyan glow-text">时间的坍缩</h2>
                    <p class="text-gray-400 uppercase tracking-widest text-sm mb-6">THE COLLAPSE OF TIME</p>
                    <p class="text-gray-400">
                        从10万年学会磨石，到200年握住手机。时间尺度，不是在线性缩短，而是在<strong class="text-white">指数性坍缩</strong>。每一个时代的产物，都成为下一个时代创新的“垫脚石”，形成滚雪球效应。
                    </p>
                </div>
                <div class="lg:col-span-2 h-64 flex items-center justify-center card p-4">
                    <!-- Simple CSS visualization of exponential growth -->
                     <svg class="w-full h-full" viewBox="0 0 300 150" preserveAspectRatio="xMidYMid meet">
                        <defs>
                            <linearGradient id="curveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#00f6ff;stop-opacity:0" />
                                <stop offset="100%" style="stop-color:#00f6ff;stop-opacity:1" />
                            </linearGradient>
                            <filter id="glow">
                                <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
                                <feMerge>
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                        </defs>
                        <path d="M 20,130 Q 150,130 200,80 T 280,20" stroke="url(#curveGradient)" stroke-width="3" fill="none" filter="url(#glow)"/>
                        <text x="25" y="125" font-size="10" fill="#666">石器时代</text>
                        <text x="100" y="125" font-size="10" fill="#888">农业革命</text>
                        <text x="180" y="125" font-size="10" fill="#aaa">工业革命</text>
                        <text x="250" y="125" font-size="10" fill="#e0e0e0">信息时代</text>
                    </svg>
                </div>
            </div>
        </section>

        <!-- Divider -->
        <div class="divider"></div>

        <!-- Section 2: Moore vs Huang -->
        <section class="text-center mb-24 md:mb-32">
            <h2 class="section-title mb-4">从摩尔定律到“黄氏定律”</h2>
            <p class="text-gray-400 uppercase tracking-widest text-sm mb-12">AI IS REWRITING THE ACCELERATION</p>
            <div class="flex flex-col md:flex-row justify-center items-stretch gap-8">
                <div class="card p-8 md:w-1/3 text-left">
                    <h3 class="text-2xl font-bold mb-2">摩尔定律</h3>
                    <p class="text-sm uppercase text-gray-400 mb-4">MOORE'S LAW (INTEL)</p>
                    <p class="text-5xl font-black highlight-cyan glow-text mb-4">32x</p>
                    <p class="text-gray-400">在特定时间内，性能约每18-24个月翻一倍。它像一个精准的节拍器，驱动了数十年的信息革命。</p>
                </div>

                <div class="flex items-center justify-center">
                    <div class="vertical-line h-24"></div>
                </div>

                <div class="card p-8 md:w-1/3 text-left border-2 border-cyan-400 shadow-2xl shadow-cyan-500/10">
                    <h3 class="text-2xl font-bold mb-2">AI芯片定律</h3>
                    <p class="text-sm uppercase text-gray-400 mb-4">HUANG'S LAW (NVIDIA)</p>
                    <p class="text-7xl font-black accent-gradient-text glow-text mb-4">1000x</p>
                    <p class="text-gray-400">当旧范式潜力耗尽，新范式开启更陡峭的增长曲线。AI专用架构的革命，让曾经的指数增长，都显得<strong class="text-white">平缓</strong>。</p>
                </div>
            </div>
        </section>

        <!-- Divider -->
        <div class="divider"></div>

        <!-- Section 3: Three Mutations -->
        <section class="mb-24 md:mb-32">
            <h2 class="section-title text-center mb-4">为什么AI让一切变得“奇怪”？</h2>
            <p class="text-gray-400 uppercase tracking-widest text-sm text-center mb-16">THREE FUNDAMENTAL MUTATIONS</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Mutation 1 -->
                <div class="card p-8">
                    <div class="text-4xl highlight-cyan glow-text mb-4"><i class="fas fa-cogs"></i></div>
                    <h3 class="text-xl font-bold mb-2">突变一：从“工具”到“能制造工具的工具”</h3>
                    <p class="text-xs uppercase text-gray-500 mb-4">Recursive Self-Improvement</p>
                    <p class="text-gray-400">AI可以设计更强的AI芯片，发现更优的AI算法。这是一个前所未有的、递归的自我改进循环。AI不再是锤子，而是能自己打造更好锤子的<strong class="text-white">“智能工匠”</strong>。</p>
                </div>
                <!-- Mutation 2 -->
                <div class="card p-8">
                    <div class="text-4xl highlight-cyan glow-text mb-4"><i class="fas fa-atom"></i></div>
                    <h3 class="text-xl font-bold mb-2">突变二：从“数据处理”到“现实模拟”</h3>
                    <p class="text-xs uppercase text-gray-500 mb-4">Decoding Reality's OS</p>
                    <p class="text-gray-400">AI不再仅处理我们理解的规则，它正在解码现实世界的操作系统。从蛋白质折叠到人类心智，它正从“计算器”跃升为<strong class="text-white">“发现引擎”</strong>。</p>
                </div>
                <!-- Mutation 3 -->
                <div class="card p-8">
                    <div class="text-4xl highlight-cyan glow-text mb-4"><i class="fas fa-project-diagram"></i></div>
                    <h3 class="text-xl font-bold mb-2">突变三：从“单一领域”到“全域赋能”</h3>
                    <p class="text-xs uppercase text-gray-500 mb-4">The Universal Catalyst</p>
                    <p class="text-gray-400">AI是“元技术”，像水和电一样渗透所有领域，催化其变革。它不是新高速公路，而是让你所有道路都变成<strong class="text-white">磁悬浮轨道</strong>的底层技术。</p>
                </div>
            </div>
        </section>

        <!-- Conclusion -->
        <footer class="text-center border-t-2 border-cyan-900 pt-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">给每个“眩晕”的我们的启示</h2>
            <div class="max-w-3xl mx-auto text-left space-y-4 text-gray-300 mb-12">
                <p><strong class="highlight-cyan">1. 技能“半衰期”急剧缩短：</strong> 适应能力是你唯一的护城河。</p>
                <p><strong class="highlight-cyan">2. 线性规划正在失效：</strong> 敏捷、实验和快速迭代是生存之道。</p>
                <p><strong class="highlight-cyan">3. 我们是“铰链一代”：</strong> 我们正处在历史的转折点，每一个决定都影响深远。</p>
            </div>
            <p class="text-2xl md:text-3xl font-bold text-white">游戏规则，确实变得“奇怪”了。</p>
            <p class="text-2xl md:text-3xl font-bold accent-gradient-text glow-text mt-2">而我们每个人，都必须学着成为这个新世界里的玩家。</p>
        </footer>

    </div>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>